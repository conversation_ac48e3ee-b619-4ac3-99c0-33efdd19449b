Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f429_439xx.o(RESET) refers to startup_stm32f429_439xx.o(STACK) for __initial_sp
    startup_stm32f429_439xx.o(RESET) refers to startup_stm32f429_439xx.o(.text) for Reset_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f429_439xx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.TIM8_CC_IRQHandler) for TIM8_CC_IRQHandler
    startup_stm32f429_439xx.o(RESET) refers to bsp_adc.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f429_439xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f429_439xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f4xx_cryp.o(i.CRYP_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_dac.o(i.DAC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(i.DMA2D_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f4xx_fmc.o(i.FMC_NORSRAMStructInit) refers to stm32f4xx_fmc.o(.constdata) for FMC_DefaultTimingStruct
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(i.HASH_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_ltdc.o(i.LTDC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(i.PWR_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_rng.o(i.RNG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_sai.o(i.SAI_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(i.SDIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_wwdg.o(i.WWDG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to bsp_adc.o(i.Rheostat_Init) for Rheostat_Init
    main.o(i.main) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to printfa.o(i.__0printf) for __2printf
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_it.o(.data) for num
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to usart.o(.data) for DEBUG_USARTx
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to bsp_adc.o(.data) for arrv
    stm32f4xx_it.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    stm32f4xx_it.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    stm32f4xx_it.o(i.TIM8_CC_IRQHandler) refers to stm32f4xx_it.o(.data) for num
    bsp_advance_tim.o(i.TIM_Mode_Config) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    bsp_advance_tim.o(i.TIM_Mode_Config) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    bsp_advance_tim.o(i.TIM_Mode_Config) refers to stm32f4xx_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    bsp_advance_tim.o(i.TIM_Mode_Config) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    bsp_advance_tim.o(i.TIM_Mode_Config) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    bsp_advance_tim.o(i.TIM_PWMINPUT_Config) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp_advance_tim.o(i.TIM_PWMINPUT_Config) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    bsp_advance_tim.o(i.TIM_PWMINPUT_Config) refers to stm32f4xx_tim.o(i.TIM_PWMIConfig) for TIM_PWMIConfig
    bsp_advance_tim.o(i.TIM_PWMINPUT_Config) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    bsp_advance_tim.o(i.TIM_PWMINPUT_Config) refers to stm32f4xx_tim.o(i.TIM_SelectSlaveMode) for TIM_SelectSlaveMode
    bsp_advance_tim.o(i.TIM_PWMINPUT_Config) refers to stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode) for TIM_SelectMasterSlaveMode
    bsp_advance_tim.o(i.TIM_PWMINPUT_Config) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    bsp_advance_tim.o(i.TIM_PWMINPUT_Config) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    bsp_advance_tim.o(i.TIMx_Configuration) refers to bsp_advance_tim.o(i.TIMx_GPIO_Config) for TIMx_GPIO_Config
    bsp_advance_tim.o(i.TIMx_Configuration) refers to bsp_advance_tim.o(i.TIMx_NVIC_Configuration) for TIMx_NVIC_Configuration
    bsp_advance_tim.o(i.TIMx_Configuration) refers to bsp_advance_tim.o(i.TIM_Mode_Config) for TIM_Mode_Config
    bsp_advance_tim.o(i.TIMx_Configuration) refers to bsp_advance_tim.o(i.TIM_PWMINPUT_Config) for TIM_PWMINPUT_Config
    bsp_advance_tim.o(i.TIMx_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp_advance_tim.o(i.TIMx_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    bsp_advance_tim.o(i.TIMx_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_advance_tim.o(i.TIMx_NVIC_Configuration) refers to misc.o(i.NVIC_Init) for NVIC_Init
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for USART_RX_STA
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(i.fputc) refers to usart.o(.data) for DEBUG_USARTx
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to memseta.o(.text) for __aeabi_memclr4
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to printfa.o(i.__0printf) for __2printf
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to arm_cfft_radix4_f32.o(.text) for arm_cfft_radix4_f32
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to arm_cmplx_mag_f32.o(.text) for arm_cmplx_mag_f32
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to f2d.o(.text) for __aeabi_f2d
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to usart.o(.data) for DEBUG_USARTx
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to bsp_adc.o(.bss) for ADC_ConvertedValue
    bsp_adc.o(i.DMA2_Stream0_IRQHandler) refers to bsp_adc.o(.data) for vpp_m
    bsp_adc.o(i.Rheostat_ADC_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp_adc.o(i.Rheostat_ADC_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_adc.o(i.ADC_StructInit) for ADC_StructInit
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    bsp_adc.o(i.Rheostat_ADC_Mode_Config) refers to bsp_adc.o(.bss) for ADC_ConvertedValue
    bsp_adc.o(i.Rheostat_Init) refers to bsp_adc.o(i.Rheostat_ADC_GPIO_Config) for Rheostat_ADC_GPIO_Config
    bsp_adc.o(i.Rheostat_Init) refers to bsp_adc.o(i.Rheostat_ADC_Mode_Config) for Rheostat_ADC_Mode_Config
    bsp_adc.o(i.Rheostat_Init) refers to arm_cfft_radix4_init_f32.o(.text) for arm_cfft_radix4_init_f32
    bsp_adc.o(i.Rheostat_Init) refers to bsp_adc.o(i.Tim3_Init) for Tim3_Init
    bsp_adc.o(i.Rheostat_Init) refers to bsp_adc.o(.bss) for scfft
    bsp_adc.o(i.Rheostat_Init) refers to bsp_adc.o(.data) for pscv
    bsp_adc.o(i.Tim3_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    bsp_adc.o(i.Tim3_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    bsp_adc.o(i.Tim3_Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    bsp_adc.o(i.Tim3_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    bsp_adc.o(i.calculate_peak_avg) refers to memseta.o(.text) for __aeabi_memclr4
    bsp_adc.o(i.fft_getpeak) refers to arm_max_f32.o(.text) for arm_max_f32
    bsp_adc.o(i.fft_getpeak) refers to f2d.o(.text) for __aeabi_f2d
    bsp_adc.o(i.fft_getpeak) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    bsp_adc.o(i.fft_getpeak) refers to dmul.o(.text) for __aeabi_dmul
    bsp_adc.o(i.fft_getpeak) refers to ddiv.o(.text) for __aeabi_ddiv
    bsp_adc.o(i.fft_getpeak) refers to d2f.o(.text) for __aeabi_d2f
    bsp_adc.o(i.fft_getpeak) refers to dfltui.o(.text) for __aeabi_ui2d
    bsp_adc.o(i.median_filter) refers to malloc.o(i.malloc) for malloc
    bsp_adc.o(i.median_filter) refers to qsort.o(.text) for qsort
    bsp_adc.o(i.median_filter) refers to malloc.o(i.free) for free
    bsp_adc.o(i.median_filter) refers to bsp_adc.o(i.compare_ints) for compare_ints
    arm_cfft_radix4_f32.o(.text) refers to arm_bitreversal.o(.text) for arm_bitreversal_f32
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevTable
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f429_439xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f429_439xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f429_439xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f429_439xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429_439xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429_439xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f429_439xx.o(HEAP), (512 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing system_stm32f4xx.o(.data), (20 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (38 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (32 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (44 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (44 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (44 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f4xx_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Cmd), (36 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DMACmd), (36 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataIn), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataOut), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DeInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_FIFOFlush), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetCmdStatus), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetFlagStatus), (40 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetITStatus), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_ITConfig), (36 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVInit), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVStructInit), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Init), (124 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyInit), (40 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyStructInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_PhaseConfig), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_RestoreContext), (148 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_SaveContext), (264 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_StructInit), (12 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC), (540 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM), (1778 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR), (466 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB), (494 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM), (1308 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_CBC), (248 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_ECB), (224 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC), (280 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB), (256 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearFlag), (16 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearITPendingBit), (16 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetFlagStatus), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetITStatus), (48 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ITConfig), (40 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f4xx_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig), (32 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig), (32 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPCmd), (36 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPConfig), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CaptureCmd), (36 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearFlag), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Cmd), (36 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_DeInit), (28 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus), (52 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetITStatus), (28 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ITConfig), (36 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Init), (64 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_JPEGCmd), (36 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ReadData), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_SetEmbeddedSynchroCodes), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_StructInit), (18 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearFlag), (52 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DeInit), (344 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (20 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (20 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (12 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (68 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (10 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_AbortTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGConfig), (184 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGStart), (36 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearFlag), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeInit), (22 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig), (56 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGConfig), (184 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGStart), (36 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetITStatus), (44 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ITConfig), (32 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Init), (228 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StartTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StructInit), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Suspend), (36 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetITStatus), (24 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_Init), (148 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (108 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (108 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (112 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (136 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (28 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (28 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (52 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (52 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (28 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (48 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (52 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (52 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (80 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (80 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_SetLatency), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_ClearFlag), (88 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_ClearITPendingBit), (96 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_GetECC), (28 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_GetFlagStatus), (72 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_GetITStatus), (100 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_GetModeStatus), (32 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_ITConfig), (176 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NANDCmd), (92 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NANDDeInit), (68 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NANDECCCmd), (92 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NANDInit), (212 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NANDStructInit), (58 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NORSRAMCmd), (52 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NORSRAMInit), (320 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NORSRAMStructInit), (60 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_PCCARDCmd), (48 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_PCCARDDeInit), (40 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_PCCARDInit), (196 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_PCCARDStructInit), (60 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SDRAMCmdConfig), (32 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SDRAMDeInit), (52 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SDRAMInit), (324 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SDRAMStructInit), (64 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SDRAMWriteProtectionConfig), (56 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SetAutoRefresh_Number), (24 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SetRefreshCount), (24 bytes).
    Removing stm32f4xx_fmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (312 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (34 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash.o(i.HASH_AutoStartDigest), (36 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearFlag), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DMACmd), (36 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DataIn), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DeInit), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetDigest), (72 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetFlagStatus), (44 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetITStatus), (32 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetInFIFOWordsNbr), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ITConfig), (32 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Init), (84 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Reset), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_RestoreContext), (68 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SaveContext), (60 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr), (28 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StartDigest), (20 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StructInit), (12 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_md5.o(i.HASH_MD5), (180 bytes).
    Removing stm32f4xx_hash_md5.o(i.HMAC_MD5), (354 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HASH_SHA1), (186 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HMAC_SHA1), (362 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DeInit), (96 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig), (22 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Init), (232 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Send7bitAddress), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTCmd), (32 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTInit), (32 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTStructInit), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearFlag), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Cmd), (36 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig), (68 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DeInit), (22 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DitherCmd), (36 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetCDStatus), (24 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetITStatus), (44 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetPosStatus), (44 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetRGBWidth), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ITConfig), (32 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Init), (392 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LIPConfig), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAddress), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAlpha), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerCmd), (22 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerInit), (276 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPixelFormat), (106 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPosition), (160 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerSize), (116 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerStructInit), (48 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_PosStructInit), (8 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_RGBStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ReloadConfig), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_StructInit), (34 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_ClearFlag), (40 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode), (68 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_LowRegulatorUnderDriveCmd), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorUnderDriveCmd), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_UnderDriveCmd), (36 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (64 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (44 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (60 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearFlag), (16 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearITPendingBit), (16 bytes).
    Removing stm32f4xx_rng.o(i.RNG_Cmd), (36 bytes).
    Removing stm32f4xx_rng.o(i.RNG_DeInit), (20 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetITStatus), (24 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetRandomNumber), (12 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ITConfig), (36 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmCmd), (116 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmStructInit), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig), (52 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Bcd2ToByte), (22 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_BypassShadowCmd), (60 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ByteToBcd2), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputCmd), (60 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputConfig), (48 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearFlag), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearITPendingBit), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig), (56 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DateStructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig), (56 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DeInit), (212 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_EnterInitMode), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ExitInitMode), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarm), (116 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarmSubSecond), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetDate), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetFlagStatus), (40 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetITStatus), (64 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetStoreOperation), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetSubSecond), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTime), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStamp), (156 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStampSubSecond), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetWakeUpCounter), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ITConfig), (100 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Init), (100 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputConfig), (56 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputTypeConfig), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ReadBackupRegister), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_RefClockCmd), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetAlarm), (236 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetDate), (200 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetTime), (208 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_StructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig), (124 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperCmd), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperFilterConfig), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinSelection), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampCmd), (56 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd), (36 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStructInit), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WaitForSynchro), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig), (48 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpCmd), (120 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteBackupRegister), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd), (28 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearFlag), (8 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearITPendingBit), (8 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Cmd), (22 bytes).
    Removing stm32f4xx_sai.o(i.SAI_CompandingModeConfig), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DMACmd), (22 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DeInit), (36 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FlushFIFO), (10 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameInit), (44 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameStructInit), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetCmdStatus), (20 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFIFOStatus), (12 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFlagStatus), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetITStatus), (30 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ITConfig), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Init), (72 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MonoModeConfig), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteModeCmd), (22 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteValueConfig), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ReceiveData), (6 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SendData), (4 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotInit), (36 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotStructInit), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_StructInit), (28 bytes).
    Removing stm32f4xx_sai.o(i.SAI_TRIStateConfig), (18 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataConfig), (52 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DeInit), (22 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetPowerState), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.I2S_FullDuplexConfig), (80 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Init), (408 bytes).
    Removing stm32f4xx_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f4xx_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DeInit), (176 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f4xx_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TIModeCmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (22 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (64 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (12 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (58 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (400 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (110 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1Init), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (120 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (86 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (32 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (240 bytes).
    Removing stm32f4xx_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.bss), (60 bytes).
    Removing bsp_advance_tim.o(.rev16_text), (4 bytes).
    Removing bsp_advance_tim.o(.revsh_text), (4 bytes).
    Removing bsp_advance_tim.o(.rrx_text), (6 bytes).
    Removing bsp_advance_tim.o(i.TIM_Mode_Config), (72 bytes).
    Removing bsp_advance_tim.o(i.TIM_PWMINPUT_Config), (156 bytes).
    Removing bsp_advance_tim.o(i.TIMx_Configuration), (20 bytes).
    Removing bsp_advance_tim.o(i.TIMx_GPIO_Config), (96 bytes).
    Removing bsp_advance_tim.o(i.TIMx_NVIC_Configuration), (64 bytes).
    Removing bsp_debug_usart.o(.rev16_text), (4 bytes).
    Removing bsp_debug_usart.o(.revsh_text), (4 bytes).
    Removing bsp_debug_usart.o(.rrx_text), (6 bytes).
    Removing bsp_debug_usart.o(i.Debug_USART_Config), (144 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i._sys_exit), (4 bytes).
    Removing bsp_adc.o(.rev16_text), (4 bytes).
    Removing bsp_adc.o(.revsh_text), (4 bytes).
    Removing bsp_adc.o(.rrx_text), (6 bytes).
    Removing bsp_adc.o(i.calculate_peak_avg), (296 bytes).
    Removing bsp_adc.o(i.compare_ints), (32 bytes).
    Removing bsp_adc.o(i.fft_getpeak), (560 bytes).
    Removing bsp_adc.o(i.median_average), (260 bytes).
    Removing bsp_adc.o(i.median_filter), (176 bytes).
    Removing bsp_adc.o(.constdata), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.rev16_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.revsh_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.rrx_text), (6 bytes).
    Removing arm_cfft_radix4_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.rrx_text), (6 bytes).
    Removing arm_cfft_radix4_init_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.rrx_text), (6 bytes).
    Removing arm_max_f32.o(.rev16_text), (4 bytes).
    Removing arm_max_f32.o(.revsh_text), (4 bytes).
    Removing arm_max_f32.o(.rrx_text), (6 bytes).
    Removing arm_max_f32.o(.text), (216 bytes).
    Removing arm_bitreversal.o(.rev16_text), (4 bytes).
    Removing arm_bitreversal.o(.revsh_text), (4 bytes).
    Removing arm_bitreversal.o(.rrx_text), (6 bytes).
    Removing arm_common_tables.o(.rev16_text), (4 bytes).
    Removing arm_common_tables.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.rrx_text), (6 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (192 bytes).
    Removing arm_common_tables.o(.constdata), (384 bytes).
    Removing arm_common_tables.o(.constdata), (768 bytes).
    Removing arm_common_tables.o(.constdata), (1536 bytes).
    Removing arm_common_tables.o(.constdata), (3072 bytes).
    Removing arm_common_tables.o(.constdata), (6144 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (24576 bytes).
    Removing arm_common_tables.o(.constdata), (48 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (192 bytes).
    Removing arm_common_tables.o(.constdata), (384 bytes).
    Removing arm_common_tables.o(.constdata), (768 bytes).
    Removing arm_common_tables.o(.constdata), (1536 bytes).
    Removing arm_common_tables.o(.constdata), (3072 bytes).
    Removing arm_common_tables.o(.constdata), (6144 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (40 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (416 bytes).
    Removing arm_common_tables.o(.constdata), (880 bytes).
    Removing arm_common_tables.o(.constdata), (896 bytes).
    Removing arm_common_tables.o(.constdata), (3600 bytes).
    Removing arm_common_tables.o(.constdata), (7616 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (24 bytes).
    Removing arm_common_tables.o(.constdata), (48 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (224 bytes).
    Removing arm_common_tables.o(.constdata), (480 bytes).
    Removing arm_common_tables.o(.constdata), (960 bytes).
    Removing arm_common_tables.o(.constdata), (1984 bytes).
    Removing arm_common_tables.o(.constdata), (3968 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (2052 bytes).
    Removing arm_common_tables.o(.constdata), (2052 bytes).
    Removing arm_common_tables.o(.constdata), (1026 bytes).
    Removing dfltui.o(.text), (26 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing fepilogue.o(.text), (110 bytes).
    Removing dneg.o(.text), (6 bytes).

903 unused section(s) (total 220226 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdlib/qsort.c          0x00000000   Number         0  qsort.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ..\..\Libraries\CMSIS\Device\ST\STM32F4xx\Source\Templates\arm\startup_stm32f429_439xx.s 0x00000000   Number         0  startup_stm32f429_439xx.o ABSOLUTE
    ..\..\Libraries\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_crc.c 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp.c 0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp_aes.c 0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp_des.c 0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp_tdes.c 0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dbgmcu.c 0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dcmi.c 0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma2d.c 0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_fmc.c 0x00000000   Number         0  stm32f4xx_fmc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_hash.c 0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_hash_md5.c 0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_hash_sha1.c 0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_i2c.c 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_iwdg.c 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_ltdc.c 0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_pwr.c 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rng.c 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rtc.c 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_sai.c 0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_sdio.c 0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_wwdg.c 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\..\Source\CommonTables\arm_common_tables.c 0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\..\Source\ComplexMathFunctions\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\..\Source\StatisticsFunctions\arm_max_f32.c 0x00000000   Number         0  arm_max_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_bitreversal.c 0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    ..\..\User\adc\bsp_adc.c                 0x00000000   Number         0  bsp_adc.o ABSOLUTE
    ..\..\User\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    ..\..\User\stm32f4xx_it.c                0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\..\User\tim\bsp_advance_tim.c         0x00000000   Number         0  bsp_advance_tim.o ABSOLUTE
    ..\..\User\usart2\usart.c                0x00000000   Number         0  usart.o ABSOLUTE
    ..\..\User\usart\bsp_debug_usart.c       0x00000000   Number         0  bsp_debug_usart.o ABSOLUTE
    ..\\..\\Libraries\\CMSIS\\Device\\ST\\STM32F4xx\\Source\\Templates\\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_crc.c 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp.c 0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp_aes.c 0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp_des.c 0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp_tdes.c 0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dbgmcu.c 0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dcmi.c 0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dma2d.c 0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_fmc.c 0x00000000   Number         0  stm32f4xx_fmc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_hash.c 0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_hash_md5.c 0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_hash_sha1.c 0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_i2c.c 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_iwdg.c 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_ltdc.c 0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_pwr.c 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rng.c 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rtc.c 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_sai.c 0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_sdio.c 0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_wwdg.c 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\..\\Source\\CommonTables\\arm_common_tables.c 0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\\..\\Source\\ComplexMathFunctions\\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\\..\\Source\\StatisticsFunctions\\arm_max_f32.c 0x00000000   Number         0  arm_max_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_bitreversal.c 0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    ..\\..\\User\\adc\\bsp_adc.c             0x00000000   Number         0  bsp_adc.o ABSOLUTE
    ..\\..\\User\\main.c                     0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\User\\stm32f4xx_it.c             0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\..\\User\\tim\\bsp_advance_tim.c     0x00000000   Number         0  bsp_advance_tim.o ABSOLUTE
    ..\\..\\User\\usart2\\usart.c            0x00000000   Number         0  usart.o ABSOLUTE
    ..\\..\\User\\usart\\bsp_debug_usart.c   0x00000000   Number         0  bsp_debug_usart.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429_439xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080001bc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001bc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001bc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001bc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c0   Section       36  startup_stm32f429_439xx.o(.text)
    $v0                                      0x080001c0   Number         0  startup_stm32f429_439xx.o(.text)
    .text                                    0x080001e4   Section        0  arm_cmplx_mag_f32.o(.text)
    .text                                    0x080002e0   Section        0  arm_cfft_radix4_f32.o(.text)
    .text                                    0x08000980   Section        0  arm_cfft_radix4_init_f32.o(.text)
    .text                                    0x08000a40   Section        0  arm_bitreversal.o(.text)
    .text                                    0x08000c16   Section        0  memseta.o(.text)
    .text                                    0x08000c3a   Section        0  dmul.o(.text)
    .text                                    0x08000d1e   Section        0  ddiv.o(.text)
    .text                                    0x08000dfc   Section        0  f2d.o(.text)
    .text                                    0x08000e22   Section        0  uidiv.o(.text)
    .text                                    0x08000e4e   Section        0  uldiv.o(.text)
    .text                                    0x08000eb0   Section        0  iusefp.o(.text)
    .text                                    0x08000eb0   Section        0  depilogue.o(.text)
    .text                                    0x08000f6a   Section        0  dadd.o(.text)
    .text                                    0x080010b8   Section        0  dfixul.o(.text)
    .text                                    0x080010e8   Section       48  cdrcmple.o(.text)
    .text                                    0x08001118   Section       36  init.o(.text)
    .text                                    0x0800113c   Section        0  llshl.o(.text)
    .text                                    0x0800115a   Section        0  llushr.o(.text)
    .text                                    0x0800117a   Section        0  llsshr.o(.text)
    i.ADC_Cmd                                0x0800119e   Section        0  stm32f4xx_adc.o(i.ADC_Cmd)
    i.ADC_CommonInit                         0x080011b4   Section        0  stm32f4xx_adc.o(i.ADC_CommonInit)
    i.ADC_DMACmd                             0x080011e4   Section        0  stm32f4xx_adc.o(i.ADC_DMACmd)
    i.ADC_Init                               0x080011fc   Section        0  stm32f4xx_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x08001250   Section        0  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_StructInit                         0x08001308   Section        0  stm32f4xx_adc.o(i.ADC_StructInit)
    i.BusFault_Handler                       0x0800131c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA2_Stream0_IRQHandler                0x08001320   Section        0  bsp_adc.o(i.DMA2_Stream0_IRQHandler)
    i.DMA_ClearITPendingBit                  0x080017a4   Section        0  stm32f4xx_dma.o(i.DMA_ClearITPendingBit)
    i.DMA_Cmd                                0x080017d8   Section        0  stm32f4xx_dma.o(i.DMA_Cmd)
    i.DMA_GetITStatus                        0x080017f0   Section        0  stm32f4xx_dma.o(i.DMA_GetITStatus)
    i.DMA_ITConfig                           0x08001854   Section        0  stm32f4xx_dma.o(i.DMA_ITConfig)
    i.DMA_Init                               0x08001890   Section        0  stm32f4xx_dma.o(i.DMA_Init)
    i.DebugMon_Handler                       0x080018e8   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x080018ea   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x0800197a   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.HardFault_Handler                      0x080019c0   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x080019c4   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080019c8   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080019cc   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08001a44   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x08001a58   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RCC_AHB1PeriphClockCmd                 0x08001a5c   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x08001a7c   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001a9c   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08001abc   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.Rheostat_ADC_GPIO_Config               0x08001ba4   Section        0  bsp_adc.o(i.Rheostat_ADC_GPIO_Config)
    Rheostat_ADC_GPIO_Config                 0x08001ba5   Thumb Code    36  bsp_adc.o(i.Rheostat_ADC_GPIO_Config)
    i.Rheostat_ADC_Mode_Config               0x08001bcc   Section        0  bsp_adc.o(i.Rheostat_ADC_Mode_Config)
    Rheostat_ADC_Mode_Config                 0x08001bcd   Thumb Code   232  bsp_adc.o(i.Rheostat_ADC_Mode_Config)
    i.Rheostat_Init                          0x08001cc4   Section        0  bsp_adc.o(i.Rheostat_Init)
    i.SVC_Handler                            0x08001cfc   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08001d00   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x08001d01   Thumb Code   272  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_Handler                        0x08001e20   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08001e24   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x08001e8c   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM8_CC_IRQHandler                     0x08001f94   Section        0  stm32f4xx_it.o(i.TIM8_CC_IRQHandler)
    i.TIM_ClearITPendingBit                  0x08001fc4   Section        0  stm32f4xx_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08001fca   Section        0  stm32f4xx_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x08001fe2   Section        0  stm32f4xx_tim.o(i.TIM_GetITStatus)
    i.TIM_SelectOutputTrigger                0x08002004   Section        0  stm32f4xx_tim.o(i.TIM_SelectOutputTrigger)
    i.TIM_TimeBaseInit                       0x08002018   Section        0  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    i.Tim3_Init                              0x0800209c   Section        0  bsp_adc.o(i.Tim3_Init)
    i.USART1_IRQHandler                      0x080020d8   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART_Cmd                              0x08002160   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08002178   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x080021cc   Section        0  stm32f4xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08002218   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x080022ec   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x080022f6   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0printf                              0x080022fc   Section        0  printfa.o(i.__0printf)
    i.__scatterload_copy                     0x0800231c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800232a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800232c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x0800233c   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x0800233d   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x080024c0   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x080024c1   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08002b9c   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08002b9d   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08002bc0   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08002bc1   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i.fputc                                  0x08002bf0   Section        0  usart.o(i.fputc)
    i.main                                   0x08002c10   Section        0  main.o(i.main)
    i.uart_init                              0x08002c34   Section        0  usart.o(i.uart_init)
    .constdata                               0x08002ce0   Section     2048  arm_common_tables.o(.constdata)
    .constdata                               0x080034e0   Section    32768  arm_common_tables.o(.constdata)
    .data                                    0x20000000   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000010   Section       68  stm32f4xx_it.o(.data)
    .data                                    0x20000054   Section       10  usart.o(.data)
    .data                                    0x20000060   Section       72  bsp_adc.o(.data)
    .bss                                     0x200000a8   Section      200  usart.o(.bss)
    .bss                                     0x20000170   Section    114928  bsp_adc.o(.bss)
    STACK                                    0x2001c260   Section     1024  startup_stm32f429_439xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429_439xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429_439xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429_439xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001bd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001bd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001c1   Thumb Code     8  startup_stm32f429_439xx.o(.text)
    ADC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CRYP_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DCMI_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2D_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    ETH_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI0_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI1_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI2_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI3_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI4_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    FLASH_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    FMC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    FPU_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    LTDC_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    LTDC_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_FS_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    PVD_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    RCC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SAI1_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SDIO_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI1_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI2_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI3_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI4_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI5_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI6_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM3_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM4_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM5_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM7_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    UART4_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    UART5_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    UART7_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    UART8_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    USART2_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    USART3_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    USART6_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    WWDG_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    arm_cmplx_mag_f32                        0x080001e5   Thumb Code   246  arm_cmplx_mag_f32.o(.text)
    arm_radix4_butterfly_f32                 0x080002e1   Thumb Code   800  arm_cfft_radix4_f32.o(.text)
    arm_radix4_butterfly_inverse_f32         0x08000601   Thumb Code   836  arm_cfft_radix4_f32.o(.text)
    arm_cfft_radix4_f32                      0x08000945   Thumb Code    60  arm_cfft_radix4_f32.o(.text)
    arm_cfft_radix4_init_f32                 0x08000981   Thumb Code   146  arm_cfft_radix4_init_f32.o(.text)
    arm_bitreversal_f32                      0x08000a41   Thumb Code   178  arm_bitreversal.o(.text)
    arm_bitreversal_q31                      0x08000af3   Thumb Code   170  arm_bitreversal.o(.text)
    arm_bitreversal_q15                      0x08000b9d   Thumb Code   122  arm_bitreversal.o(.text)
    __aeabi_memset                           0x08000c17   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000c17   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000c17   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000c25   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000c25   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000c25   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000c29   Thumb Code    18  memseta.o(.text)
    __aeabi_dmul                             0x08000c3b   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000d1f   Thumb Code   222  ddiv.o(.text)
    __aeabi_f2d                              0x08000dfd   Thumb Code    38  f2d.o(.text)
    __aeabi_uidiv                            0x08000e23   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000e23   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x08000e4f   Thumb Code    98  uldiv.o(.text)
    __I$use$fp                               0x08000eb1   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x08000eb1   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000ecf   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x08000f6b   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080010ad   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080010b3   Thumb Code     6  dadd.o(.text)
    __aeabi_d2ulz                            0x080010b9   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x080010e9   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08001119   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08001119   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x0800113d   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800113d   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800115b   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800115b   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0800117b   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x0800117b   Thumb Code     0  llsshr.o(.text)
    ADC_Cmd                                  0x0800119f   Thumb Code    22  stm32f4xx_adc.o(i.ADC_Cmd)
    ADC_CommonInit                           0x080011b5   Thumb Code    34  stm32f4xx_adc.o(i.ADC_CommonInit)
    ADC_DMACmd                               0x080011e5   Thumb Code    22  stm32f4xx_adc.o(i.ADC_DMACmd)
    ADC_Init                                 0x080011fd   Thumb Code    74  stm32f4xx_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x08001251   Thumb Code   184  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    ADC_StructInit                           0x08001309   Thumb Code    20  stm32f4xx_adc.o(i.ADC_StructInit)
    BusFault_Handler                         0x0800131d   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DMA2_Stream0_IRQHandler                  0x08001321   Thumb Code   972  bsp_adc.o(i.DMA2_Stream0_IRQHandler)
    DMA_ClearITPendingBit                    0x080017a5   Thumb Code    38  stm32f4xx_dma.o(i.DMA_ClearITPendingBit)
    DMA_Cmd                                  0x080017d9   Thumb Code    22  stm32f4xx_dma.o(i.DMA_Cmd)
    DMA_GetITStatus                          0x080017f1   Thumb Code    82  stm32f4xx_dma.o(i.DMA_GetITStatus)
    DMA_ITConfig                             0x08001855   Thumb Code    58  stm32f4xx_dma.o(i.DMA_ITConfig)
    DMA_Init                                 0x08001891   Thumb Code    82  stm32f4xx_dma.o(i.DMA_Init)
    DebugMon_Handler                         0x080018e9   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x080018eb   Thumb Code   144  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x0800197b   Thumb Code    70  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    HardFault_Handler                        0x080019c1   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x080019c5   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080019c9   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x080019cd   Thumb Code   106  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08001a45   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x08001a59   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RCC_AHB1PeriphClockCmd                   0x08001a5d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x08001a7d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001a9d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08001abd   Thumb Code   214  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    Rheostat_Init                            0x08001cc5   Thumb Code    44  bsp_adc.o(i.Rheostat_Init)
    SVC_Handler                              0x08001cfd   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08001e21   Thumb Code     2  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08001e25   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x08001e8d   Thumb Code   222  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM8_CC_IRQHandler                       0x08001f95   Thumb Code    40  stm32f4xx_it.o(i.TIM8_CC_IRQHandler)
    TIM_ClearITPendingBit                    0x08001fc5   Thumb Code     6  stm32f4xx_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08001fcb   Thumb Code    24  stm32f4xx_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x08001fe3   Thumb Code    34  stm32f4xx_tim.o(i.TIM_GetITStatus)
    TIM_SelectOutputTrigger                  0x08002005   Thumb Code    18  stm32f4xx_tim.o(i.TIM_SelectOutputTrigger)
    TIM_TimeBaseInit                         0x08002019   Thumb Code   104  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    Tim3_Init                                0x0800209d   Thumb Code    56  bsp_adc.o(i.Tim3_Init)
    USART1_IRQHandler                        0x080020d9   Thumb Code   122  usart.o(i.USART1_IRQHandler)
    USART_Cmd                                0x08002161   Thumb Code    24  stm32f4xx_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08002179   Thumb Code    84  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x080021cd   Thumb Code    74  stm32f4xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x08002219   Thumb Code   204  stm32f4xx_usart.o(i.USART_Init)
    USART_ReceiveData                        0x080022ed   Thumb Code    10  stm32f4xx_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x080022f7   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __0printf                                0x080022fd   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x080022fd   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x080022fd   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x080022fd   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x080022fd   Thumb Code     0  printfa.o(i.__0printf)
    __scatterload_copy                       0x0800231d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800232b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800232d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    fputc                                    0x08002bf1   Thumb Code    26  usart.o(i.fputc)
    main                                     0x08002c11   Thumb Code    32  main.o(i.main)
    uart_init                                0x08002c35   Thumb Code   164  usart.o(i.uart_init)
    armBitRevTable                           0x08002ce0   Data        2048  arm_common_tables.o(.constdata)
    twiddleCoef_4096                         0x080034e0   Data       32768  arm_common_tables.o(.constdata)
    Region$$Table$$Base                      0x0800b4e0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800b500   Number         0  anon$$obj.o(Region$$Table)
    top_watch                                0x20000010   Data           4  stm32f4xx_it.o(.data)
    i                                        0x20000014   Data           1  stm32f4xx_it.o(.data)
    pul_num                                  0x20000018   Data           4  stm32f4xx_it.o(.data)
    cnt                                      0x2000001c   Data           4  stm32f4xx_it.o(.data)
    last_cnt                                 0x20000020   Data           4  stm32f4xx_it.o(.data)
    frq_sum                                  0x20000024   Data           4  stm32f4xx_it.o(.data)
    Frq                                      0x20000028   Data           4  stm32f4xx_it.o(.data)
    c                                        0x2000002c   Data           2  stm32f4xx_it.o(.data)
    IC2Value                                 0x2000002e   Data           2  stm32f4xx_it.o(.data)
    IC1Value                                 0x20000030   Data           8  stm32f4xx_it.o(.data)
    DutyCycle                                0x20000038   Data           4  stm32f4xx_it.o(.data)
    Frequency                                0x2000003c   Data           4  stm32f4xx_it.o(.data)
    Frequency_high                           0x20000040   Data           4  stm32f4xx_it.o(.data)
    num                                      0x20000048   Data           8  stm32f4xx_it.o(.data)
    Freq                                     0x20000050   Data           4  stm32f4xx_it.o(.data)
    DEBUG_USARTx                             0x20000054   Data           4  usart.o(.data)
    __stdout                                 0x20000058   Data           4  usart.o(.data)
    USART_RX_STA                             0x2000005c   Data           2  usart.o(.data)
    bias_voltage2                            0x20000060   Data           4  bsp_adc.o(.data)
    HZ2                                      0x20000064   Data           4  bsp_adc.o(.data)
    amplitude2                               0x20000068   Data           4  bsp_adc.o(.data)
    phase2                                   0x2000006c   Data           4  bsp_adc.o(.data)
    bias_voltage1                            0x20000070   Data           4  bsp_adc.o(.data)
    HZ1                                      0x20000074   Data           4  bsp_adc.o(.data)
    amplitude1                               0x20000078   Data           4  bsp_adc.o(.data)
    phase1                                   0x2000007c   Data           4  bsp_adc.o(.data)
    arrv                                     0x20000080   Data           2  bsp_adc.o(.data)
    pscv                                     0x20000082   Data           2  bsp_adc.o(.data)
    vpp_m                                    0x20000084   Data           4  bsp_adc.o(.data)
    vpp_max                                  0x20000088   Data           4  bsp_adc.o(.data)
    sum                                      0x2000008c   Data           4  bsp_adc.o(.data)
    count                                    0x20000090   Data           1  bsp_adc.o(.data)
    phase_difference                         0x20000094   Data           4  bsp_adc.o(.data)
    vpp                                      0x20000098   Data           2  bsp_adc.o(.data)
    maxvalue                                 0x2000009c   Data           4  bsp_adc.o(.data)
    max_pos                                  0x200000a0   Data           4  bsp_adc.o(.data)
    vpp_fin                                  0x200000a4   Data           4  bsp_adc.o(.data)
    USART_RX_BUF                             0x200000a8   Data         200  usart.o(.bss)
    filtered_data                            0x20000170   Data       16384  bsp_adc.o(.bss)
    vpp_array                                0x20004170   Data          20  bsp_adc.o(.bss)
    FFT_InputBuf                             0x20004184   Data       32768  bsp_adc.o(.bss)
    FFT_OutputBuf                            0x2000c184   Data       16384  bsp_adc.o(.bss)
    scfft                                    0x20010184   Data          20  bsp_adc.o(.bss)
    freamp                                   0x20010198   Data         200  bsp_adc.o(.bss)
    ADC_ConvertedValue                       0x20010260   Data       16384  bsp_adc.o(.bss)
    ADC_ConvertedValuelocal                  0x20014260   Data       32768  bsp_adc.o(.bss)
    __initial_sp                             0x2001c660   Data           0  startup_stm32f429_439xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000b5a8, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000b500, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_stm32f429_439xx.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         5635  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         5976    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         5979    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         5981    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         5983    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         5984    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         5986    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         5988    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         5977    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c0   0x080001c0   0x00000024   Code   RO            4    .text               startup_stm32f429_439xx.o
    0x080001e4   0x080001e4   0x000000fc   Code   RO         5395    .text               arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x080002e0   0x080002e0   0x000006a0   Code   RO         5441    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08000980   0x08000980   0x000000c0   Code   RO         5468    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x08000a40   0x08000a40   0x000001d6   Code   RO         5520    .text               arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x08000c16   0x08000c16   0x00000024   Code   RO         5640    .text               mc_w.l(memseta.o)
    0x08000c3a   0x08000c3a   0x000000e4   Code   RO         5931    .text               mf_w.l(dmul.o)
    0x08000d1e   0x08000d1e   0x000000de   Code   RO         5933    .text               mf_w.l(ddiv.o)
    0x08000dfc   0x08000dfc   0x00000026   Code   RO         5937    .text               mf_w.l(f2d.o)
    0x08000e22   0x08000e22   0x0000002c   Code   RO         5990    .text               mc_w.l(uidiv.o)
    0x08000e4e   0x08000e4e   0x00000062   Code   RO         5992    .text               mc_w.l(uldiv.o)
    0x08000eb0   0x08000eb0   0x00000000   Code   RO         6007    .text               mc_w.l(iusefp.o)
    0x08000eb0   0x08000eb0   0x000000ba   Code   RO         6010    .text               mf_w.l(depilogue.o)
    0x08000f6a   0x08000f6a   0x0000014e   Code   RO         6012    .text               mf_w.l(dadd.o)
    0x080010b8   0x080010b8   0x00000030   Code   RO         6016    .text               mf_w.l(dfixul.o)
    0x080010e8   0x080010e8   0x00000030   Code   RO         6018    .text               mf_w.l(cdrcmple.o)
    0x08001118   0x08001118   0x00000024   Code   RO         6024    .text               mc_w.l(init.o)
    0x0800113c   0x0800113c   0x0000001e   Code   RO         6026    .text               mc_w.l(llshl.o)
    0x0800115a   0x0800115a   0x00000020   Code   RO         6028    .text               mc_w.l(llushr.o)
    0x0800117a   0x0800117a   0x00000024   Code   RO         6030    .text               mc_w.l(llsshr.o)
    0x0800119e   0x0800119e   0x00000016   Code   RO          222    i.ADC_Cmd           stm32f4xx_adc.o
    0x080011b4   0x080011b4   0x00000030   Code   RO          223    i.ADC_CommonInit    stm32f4xx_adc.o
    0x080011e4   0x080011e4   0x00000016   Code   RO          226    i.ADC_DMACmd        stm32f4xx_adc.o
    0x080011fa   0x080011fa   0x00000002   PAD
    0x080011fc   0x080011fc   0x00000054   Code   RO          242    i.ADC_Init          stm32f4xx_adc.o
    0x08001250   0x08001250   0x000000b8   Code   RO          247    i.ADC_RegularChannelConfig  stm32f4xx_adc.o
    0x08001308   0x08001308   0x00000014   Code   RO          251    i.ADC_StructInit    stm32f4xx_adc.o
    0x0800131c   0x0800131c   0x00000004   Code   RO         5099    i.BusFault_Handler  stm32f4xx_it.o
    0x08001320   0x08001320   0x00000484   Code   RO         5310    i.DMA2_Stream0_IRQHandler  bsp_adc.o
    0x080017a4   0x080017a4   0x00000034   Code   RO         1200    i.DMA_ClearITPendingBit  stm32f4xx_dma.o
    0x080017d8   0x080017d8   0x00000016   Code   RO         1201    i.DMA_Cmd           stm32f4xx_dma.o
    0x080017ee   0x080017ee   0x00000002   PAD
    0x080017f0   0x080017f0   0x00000064   Code   RO         1211    i.DMA_GetITStatus   stm32f4xx_dma.o
    0x08001854   0x08001854   0x0000003a   Code   RO         1212    i.DMA_ITConfig      stm32f4xx_dma.o
    0x0800188e   0x0800188e   0x00000002   PAD
    0x08001890   0x08001890   0x00000058   Code   RO         1213    i.DMA_Init          stm32f4xx_dma.o
    0x080018e8   0x080018e8   0x00000002   Code   RO         5100    i.DebugMon_Handler  stm32f4xx_it.o
    0x080018ea   0x080018ea   0x00000090   Code   RO         1971    i.GPIO_Init         stm32f4xx_gpio.o
    0x0800197a   0x0800197a   0x00000046   Code   RO         1972    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x080019c0   0x080019c0   0x00000004   Code   RO         5101    i.HardFault_Handler  stm32f4xx_it.o
    0x080019c4   0x080019c4   0x00000004   Code   RO         5102    i.MemManage_Handler  stm32f4xx_it.o
    0x080019c8   0x080019c8   0x00000002   Code   RO         5103    i.NMI_Handler       stm32f4xx_it.o
    0x080019ca   0x080019ca   0x00000002   PAD
    0x080019cc   0x080019cc   0x00000078   Code   RO          164    i.NVIC_Init         misc.o
    0x08001a44   0x08001a44   0x00000014   Code   RO          165    i.NVIC_PriorityGroupConfig  misc.o
    0x08001a58   0x08001a58   0x00000002   Code   RO         5104    i.PendSV_Handler    stm32f4xx_it.o
    0x08001a5a   0x08001a5a   0x00000002   PAD
    0x08001a5c   0x08001a5c   0x00000020   Code   RO         2864    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08001a7c   0x08001a7c   0x00000020   Code   RO         2873    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08001a9c   0x08001a9c   0x00000020   Code   RO         2876    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x08001abc   0x08001abc   0x000000e8   Code   RO         2885    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x08001ba4   0x08001ba4   0x00000028   Code   RO         5311    i.Rheostat_ADC_GPIO_Config  bsp_adc.o
    0x08001bcc   0x08001bcc   0x000000f8   Code   RO         5312    i.Rheostat_ADC_Mode_Config  bsp_adc.o
    0x08001cc4   0x08001cc4   0x00000038   Code   RO         5313    i.Rheostat_Init     bsp_adc.o
    0x08001cfc   0x08001cfc   0x00000002   Code   RO         5105    i.SVC_Handler       stm32f4xx_it.o
    0x08001cfe   0x08001cfe   0x00000002   PAD
    0x08001d00   0x08001d00   0x00000120   Code   RO           13    i.SetSysClock       system_stm32f4xx.o
    0x08001e20   0x08001e20   0x00000002   Code   RO         5106    i.SysTick_Handler   stm32f4xx_it.o
    0x08001e22   0x08001e22   0x00000002   PAD
    0x08001e24   0x08001e24   0x00000068   Code   RO           15    i.SystemInit        system_stm32f4xx.o
    0x08001e8c   0x08001e8c   0x00000108   Code   RO         5107    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x08001f94   0x08001f94   0x00000030   Code   RO         5108    i.TIM8_CC_IRQHandler  stm32f4xx_it.o
    0x08001fc4   0x08001fc4   0x00000006   Code   RO         4231    i.TIM_ClearITPendingBit  stm32f4xx_tim.o
    0x08001fca   0x08001fca   0x00000018   Code   RO         4236    i.TIM_Cmd           stm32f4xx_tim.o
    0x08001fe2   0x08001fe2   0x00000022   Code   RO         4257    i.TIM_GetITStatus   stm32f4xx_tim.o
    0x08002004   0x08002004   0x00000012   Code   RO         4294    i.TIM_SelectOutputTrigger  stm32f4xx_tim.o
    0x08002016   0x08002016   0x00000002   PAD
    0x08002018   0x08002018   0x00000084   Code   RO         4308    i.TIM_TimeBaseInit  stm32f4xx_tim.o
    0x0800209c   0x0800209c   0x0000003c   Code   RO         5314    i.Tim3_Init         bsp_adc.o
    0x080020d8   0x080020d8   0x00000088   Code   RO         5257    i.USART1_IRQHandler  usart.o
    0x08002160   0x08002160   0x00000018   Code   RO         4790    i.USART_Cmd         stm32f4xx_usart.o
    0x08002178   0x08002178   0x00000054   Code   RO         4794    i.USART_GetITStatus  stm32f4xx_usart.o
    0x080021cc   0x080021cc   0x0000004a   Code   RO         4796    i.USART_ITConfig    stm32f4xx_usart.o
    0x08002216   0x08002216   0x00000002   PAD
    0x08002218   0x08002218   0x000000d4   Code   RO         4797    i.USART_Init        stm32f4xx_usart.o
    0x080022ec   0x080022ec   0x0000000a   Code   RO         4804    i.USART_ReceiveData  stm32f4xx_usart.o
    0x080022f6   0x080022f6   0x00000004   Code   RO         5109    i.UsageFault_Handler  stm32f4xx_it.o
    0x080022fa   0x080022fa   0x00000002   PAD
    0x080022fc   0x080022fc   0x00000020   Code   RO         5875    i.__0printf         mc_w.l(printfa.o)
    0x0800231c   0x0800231c   0x0000000e   Code   RO         6034    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800232a   0x0800232a   0x00000002   Code   RO         6035    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800232c   0x0800232c   0x0000000e   Code   RO         6036    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800233a   0x0800233a   0x00000002   PAD
    0x0800233c   0x0800233c   0x00000184   Code   RO         5882    i._fp_digits        mc_w.l(printfa.o)
    0x080024c0   0x080024c0   0x000006dc   Code   RO         5883    i._printf_core      mc_w.l(printfa.o)
    0x08002b9c   0x08002b9c   0x00000024   Code   RO         5884    i._printf_post_padding  mc_w.l(printfa.o)
    0x08002bc0   0x08002bc0   0x0000002e   Code   RO         5885    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08002bee   0x08002bee   0x00000002   PAD
    0x08002bf0   0x08002bf0   0x00000020   Code   RO         5259    i.fputc             usart.o
    0x08002c10   0x08002c10   0x00000024   Code   RO         5044    i.main              main.o
    0x08002c34   0x08002c34   0x000000ac   Code   RO         5260    i.uart_init         usart.o
    0x08002ce0   0x08002ce0   0x00000800   Data   RO         5544    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x080034e0   0x080034e0   0x00008000   Data   RO         5553    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800b4e0   0x0800b4e0   0x00000020   Data   RO         6032    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800b500, Size: 0x0001c660, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800b500   0x00000010   Data   RW         2917    .data               stm32f4xx_rcc.o
    0x20000010   0x0800b510   0x00000044   Data   RW         5111    .data               stm32f4xx_it.o
    0x20000054   0x0800b554   0x0000000a   Data   RW         5262    .data               usart.o
    0x2000005e   0x0800b55e   0x00000002   PAD
    0x20000060   0x0800b560   0x00000048   Data   RW         5322    .data               bsp_adc.o
    0x200000a8        -       0x000000c8   Zero   RW         5261    .bss                usart.o
    0x20000170        -       0x0001c0f0   Zero   RW         5320    .bss                bsp_adc.o
    0x2001c260        -       0x00000400   Zero   RW            1    STACK               startup_stm32f429_439xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1560        220          0         72     114928       8075   bsp_adc.o
        36          4          0          0          0      13979   main.o
       140         24          0          0          0       2047   misc.o
        36          8        428          0       1024       1052   startup_stm32f429_439xx.o
       380         24          0          0          0       6043   stm32f4xx_adc.o
       320         38          0          0          0       5418   stm32f4xx_dma.o
       214          0          0          0          0       2275   stm32f4xx_gpio.o
       338         50          0         68          0       7953   stm32f4xx_it.o
       328         36          0         16          0       5567   stm32f4xx_rcc.o
       214         28          0          0          0       4637   stm32f4xx_tim.o
       404          8          0          0          0       5246   stm32f4xx_usart.o
       392         32          0          0          0     323749   system_stm32f4xx.o
       340         28          0         10        200       4293   usart.o

    ----------------------------------------------------------------------
      4722        <USER>        <GROUP>        168     116152     390334   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          0          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       470          0          0          0          0       3046   arm_bitreversal.o
      1696          0          0          0          0      18824   arm_cfft_radix4_f32.o
       192         46          0          0          0        947   arm_cfft_radix4_init_f32.o
       252          6          0          0          0      17652   arm_cmplx_mag_f32.o
         0          0      34816          0          0       4704   arm_common_tables.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2258         90          0          0          0        452   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o

    ----------------------------------------------------------------------
      6338        <USER>      <GROUP>          0          0      46901   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2610         52      34816          0          0      45173   arm_cortexM4lf_math.lib
      2620        106          0          0          0       1004   mc_w.l
      1104          0          0          0          0        724   mf_w.l

    ----------------------------------------------------------------------
      6338        <USER>      <GROUP>          0          0      46901   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     11060        658      35276        168     116152     431707   Grand Totals
     11060        658      35276        168     116152     431707   ELF Image Totals
     11060        658      35276        168          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                46336 (  45.25kB)
    Total RW  Size (RW Data + ZI Data)            116320 ( 113.59kB)
    Total ROM Size (Code + RO Data + RW Data)      46504 (  45.41kB)

==============================================================================

