/**
  ******************************************************************************
  * @file    bsp_bsp_adc.c
  * <AUTHOR>
  * @version V1.0
  * @date    2015-xx-xx
  * @brief   adc驱动
  ******************************************************************************
  * @attention
  *
  * 实验平台:野火  STM32 F429 开发板  
  * 论坛    :http://www.firebbs.cn
  * 淘宝    :https://fire-stm32.taobao.com
  *
  ******************************************************************************
  */ 

#include "./adc/bsp_adc.h"
#include "arm_math.h"
#include "usart.h"
#include <math.h>
#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h> 
#include "stm32f4xx_it.h"
//#include "amplitude_calibration.h"

//#define sampledot  4096
#define FFT_LENGTH		4096		//4096点FFT
#define fft_arr 10     // 用于计算FFT采样频率的系数             
#define fft_psc 90       // 用于计算FFT采样频率的系数     
#define ADC_ARRAY_SIZE 4096  // ADC数组固定大小
#define DEFAULT_FILTER_WINDOW 5  // 默认中值滤波窗口大小（奇数）

#define SAMPLE_RATE 8192      // 采样率Hz
#define MIN_PEAK_RATIO 0.1f   // 最小峰值比例
#define FREQ_RESOLUTION ((float)SAMPLE_RATE / FFT_LENGTH)  // 动态频率分辨率
#define M_PI 3.14159265358979323846f

typedef struct {
    float frequency;          // 频率值
    float magnitude;         // 幅度值
    int bin_index;           // 频率bin索引
} FreqPeak;

uint32_t filtered_data[ADC_ARRAY_SIZE];
 float bias_voltage2,HZ2,amplitude2,phase2,bias_voltage1,HZ1,amplitude1,phase1;

const uint32_t fft_sample_freq=90000000/(fft_arr*fft_psc);  // 计算FFT采样频率:100khz
extern USART_TypeDef * DEBUG_USARTx;  

uint16_t arrv=30;
uint16_t pscv=30;

uint32_t vpp_array[5];
uint32_t vpp_m;
float vpp_max;
uint32_t sum;

uint8_t count;

float FFT_InputBuf[FFT_LENGTH*2];	 // FFT输入数组，用于存放复数
float FFT_OutputBuf[FFT_LENGTH];	 // FFT输出数组，存放幅值
arm_cfft_radix4_instance_f32 scfft;   // FFT实例结构体
//uint32_t sampledata[sampledot]={0};//用于存放ADC采样数据的数组,高16位保存adc2 pa5， 低16位保存adc1 pa6

float phase_difference=0; // 用于存放相位差的变量
float freamp[50];//用于存放各次谐波频率和幅值的数组

__IO uint32_t ADC_ConvertedValue[4096];
__IO uint32_t ADC_ConvertedValuelocal[8192];

uint16_t vpp;
float32_t maxvalue;
uint32_t max_pos;
float vpp_fin;

extern uint32_t Freq;


// 定时器3初始化函数
void Tim3_Init(uint16_t arr,uint16_t psc)  //TIM3定时器最大时钟频率为90M
{
	TIM_TimeBaseInitTypeDef   TIM_TimeBaseInitstruct;          
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3,ENABLE);          
  
	TIM_TimeBaseInitstruct.TIM_Period=arr;   
  TIM_TimeBaseInitstruct.TIM_Prescaler=psc;
	TIM_TimeBaseInitstruct.TIM_CounterMode=TIM_CounterMode_Up;
	TIM_TimeBaseInitstruct.TIM_ClockDivision=TIM_CKD_DIV1;
	TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitstruct);
	
	//TIM_ITConfig(TIM3,TIM_IT_Update,ENABLE);     
	TIM_SelectOutputTrigger(TIM3, TIM_TRGOSource_Update);	
//	TIM_Cmd(TIM3,DISABLE);
	TIM_Cmd(TIM3,ENABLE);

}

static void Rheostat_ADC_GPIO_Config(void)
{
		GPIO_InitTypeDef GPIO_InitStructure;
	
	// 使能 GPIO 时钟
	RCC_AHB1PeriphClockCmd(RHEOSTAT_ADC_GPIO_CLK1, ENABLE);
//	RCC_AHB1PeriphClockCmd(RHEOSTAT_ADC_GPIO_CLK2, ENABLE);
		
	// 配置 IO
	GPIO_InitStructure.GPIO_Pin = RHEOSTAT_ADC_GPIO_PIN1;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;	    
  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL ; //不上拉不下拉
	GPIO_Init(RHEOSTAT_ADC_GPIO_PORT1, &GPIO_InitStructure);

//	// 配置 IO
//	GPIO_InitStructure.GPIO_Pin = RHEOSTAT_ADC_GPIO_PIN2;
//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;	    
//  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL ; //不上拉不下拉
//	GPIO_Init(RHEOSTAT_ADC_GPIO_PORT2, &GPIO_InitStructure);	
}

static void Rheostat_ADC_Mode_Config(void)
{
	uint32_t index=1;
	
	DMA_InitTypeDef DMA_InitStructure;
	ADC_InitTypeDef ADC_InitStructure;
  ADC_CommonInitTypeDef ADC_CommonInitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
  // ------------------DMA Init 结构体参数 初始化--------------------------
  // ADC1使用DMA2，数据流0，通道0，这个是手册固定死的
  // 开启DMA时钟
  RCC_AHB1PeriphClockCmd(RHEOSTAT_ADC_DMA_CLK, ENABLE); 
	// 外设基址为：ADC 数据寄存器地址
	DMA_InitStructure.DMA_PeripheralBaseAddr = RHEOSTAT_ADC_CDR_ADDR;	
  // 存储器地址，实际上就是一个内部SRAM的变量	
	DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)ADC_ConvertedValue;  
  // 数据传输方向为外设到存储器	
	DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;	
	// 缓冲区大小为，指一次传输的数据量
	DMA_InitStructure.DMA_BufferSize = 4096;	
	// 外设寄存器只有一个，地址不用递增
	DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
  // 存储器地址固定
	DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable; 
  // // 外设数据大小为半字，即两个字节 
	DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Word; 
  //	存储器数据大小也为半字，跟外设数据大小相同
	DMA_InitStructure.DMA_MemoryDataSize = DMA_PeripheralDataSize_Word;	
	// 循环传输模式
	DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
  // DMA 传输通道优先级为高，当使用一个DMA通道时，优先级设置不影响
	DMA_InitStructure.DMA_Priority = DMA_Priority_High;
  // 禁止DMA FIFO	，使用直连模式
  DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;  
  // FIFO 大小，FIFO模式禁止时，这个不用配置	
  DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
  DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
  DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;  
	// 选择 DMA 通道，通道存在于流中
  DMA_InitStructure.DMA_Channel = RHEOSTAT_ADC_DMA_CHANNEL; 
  //初始化DMA流，流相当于一个大的管道，管道里面有很多通道
	DMA_Init(RHEOSTAT_ADC_DMA_STREAM, &DMA_InitStructure);
	// 使能DMA流
	DMA_ITConfig(RHEOSTAT_ADC_DMA_STREAM, DMA_IT_TC, ENABLE);
	
  DMA_Cmd(RHEOSTAT_ADC_DMA_STREAM, ENABLE);
	
	NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream0_IRQn;  //DMA2_Stream0中断
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=3;  //抢占优先级1
  NVIC_InitStructure.NVIC_IRQChannelSubPriority =2;        //子优先级1
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;            //IRQ通道使能
  NVIC_Init(&NVIC_InitStructure);
	
	
	// 开启ADC时钟
	RCC_APB2PeriphClockCmd(RHEOSTAT_ADC1_CLK , ENABLE);
//  RCC_APB2PeriphClockCmd(RHEOSTAT_ADC2_CLK , ENABLE);
	
  // -------------------ADC Common 结构体 参数 初始化------------------------
	// 独立ADC模式
  ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;
  // 时钟为fpclk x分频	
  ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;
  // 禁止DMA直接访问模式	
  ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_1;
  // 采样时间间隔	
  ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;  
  ADC_CommonInit(&ADC_CommonInitStructure);
	
  // -------------------ADC Init 结构体 参数 初始化--------------------------
	ADC_StructInit(&ADC_InitStructure);
  // ADC 分辨率
  ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;
  // 禁止扫描模式，多通道采集才需要	
  ADC_InitStructure.ADC_ScanConvMode = DISABLE; 
  // 连续转换	
  ADC_InitStructure.ADC_ContinuousConvMode = ENABLE; 
  //禁止外部边沿触发
  ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;
  //外部触发通道，本例子使用软件触发，此值随便赋值即可
  ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T3_TRGO;
  //数据右对齐	
  ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
  //转换通道 1个
  ADC_InitStructure.ADC_NbrOfConversion = 1;
	
  //---------------------------------------------------------------------------	
  ADC_Init(RHEOSTAT_ADC1, &ADC_InitStructure);	
   // 配置 ADC 通道转换顺序为1，第一个转换，采样时间为3个时钟周期
  ADC_RegularChannelConfig(RHEOSTAT_ADC1, RHEOSTAT_ADC_CHANNEL1, 1, ADC_SampleTime_3Cycles);   
  //---------------------------------------------------------------------------
//	ADC_ExternalTrigConvCmd(ADC1,ENABLE);
//	ADC_Init(RHEOSTAT_ADC2, &ADC_InitStructure);
//  // 配置 ADC 通道转换顺序为1，第一个转换，采样时间为3个时钟周期
//  ADC_RegularChannelConfig(RHEOSTAT_ADC2, RHEOSTAT_ADC_CHANNEL2, 1, ADC_SampleTime_3Cycles);   
  //---------------------------------------------------------------------------
		ADC_DMARequestAfterLastTransferCmd(RHEOSTAT_ADC1, ENABLE); 
  // 使能DMA请求 after last transfer (multi-ADC mode)
//  ADC_MultiModeDMARequestAfterLastTransferCmd(ENABLE);
// 使能ADC DMA
  ADC_DMACmd(RHEOSTAT_ADC1, ENABLE);
	
  // 使能ADC
  ADC_Cmd(RHEOSTAT_ADC1, ENABLE);  
//  ADC_Cmd(RHEOSTAT_ADC2, ENABLE);   
  //开始adc转换，软件触发
//  ADC_SoftwareStartConv(RHEOSTAT_ADC1);

//  ADC_SoftwareStartConv(RHEOSTAT_ADC2);
}

int fft_getpeak(float *inputx,float *input,float *output,uint16_t inlen,uint8_t x,uint8_t N,float y) //  intlen 输入数组长度，x寻找长度
{                                                                           
	int i,i2;
	uint32_t idex;  //不同于上一个函数中的，因为他们在不同的函数中被定义
	float datas;
	float sum;
	int outlen=0;
	for(i=0;i<inlen-x;i+=x)
	{
		arm_max_f32(input+i,x,&datas,&idex);   
		if( (input[i+idex]>=input[i+idex+1])&&(input[i+idex]>=input[i+idex-1])&&( (2*datas) /FFT_LENGTH )>y)   
		   {
			   sum=0;   
			   for(i2=i+idex-N;i2<i+idex+N;i2++)   
			   {
				   sum+=input[i2];          
			   }        
			   if(1.5f*sum/(2*N)<datas)       
			   {                                                                                             
				     output[3*outlen+2] = atan2(inputx[2*(i+idex+1)+1],inputx[2*(i+idex+1)])*180/3.1415926f;	//计算相位		   
				     output[3*outlen+1] = 1.0f*(2*datas)/FFT_LENGTH;   //计算幅度
					   output[3*outlen] = 1.0*fft_sample_freq*(i+idex+1)/FFT_LENGTH;//计算频率
					   outlen++;				   
			   }                                                                                               
               else continue;			   
		   }
			
		else continue;
		
	}
	return outlen;
	
	
}

float calculate_peak_avg(volatile uint32_t* adc_data, int sample_times) {
		uint16_t cnt;
    uint8_t flag[1024];  // 标记数组（与原始代码一致）
    uint16_t i, j, temp;
    float vmax, vmin;
    float sum_vpp = 0.0f;   // 峰峰值累加和

    for (cnt = 0; cnt < sample_times; cnt++) {
        // 计算第N大值（保持原始逻辑）
        memset(flag, 0, sizeof(flag));
        for (i = 1; i <= 5; i++) {
            vmax = 0.0;
            for (j = 0; j < 1024; j++) {
                if (flag[j] == 1) continue;
                if (vmax < adc_data[j]) {
                    vmax = adc_data[j];
                    temp = j;
                }
            }
            flag[temp] = 1;
        }

        // 计算第N小值（保持原始逻辑）
        memset(flag, 0, sizeof(flag));
        for (i = 1; i <= 5; i++) {
            vmin = 4096.0;
            for (j = 0; j < 1024; j++) {
                if (flag[j] == 1) continue;
                if (vmin > adc_data[j]) {
                    vmin = adc_data[j];
                    temp = j;
                }
            }
            flag[temp] = 1;
        }

        sum_vpp += (vmax - vmin);  // 累加当前峰峰值
    }

    return sum_vpp / sample_times;  // 返回均值
}

//static void swap(uint32_t* a, uint32_t* b) {
//    uint32_t temp = *a;
//    *a = *b;
//    *b = temp;
//}

///**
// * @brief 冒泡排序（小数据量适用）
// */
//static void bubble_sort(uint32_t* arr, uint16_t size) {
//    for (uint16_t i = 0; i < size - 1; i++) {
//        for (uint16_t j = 0; j < size - i - 1; j++) {
//            if (arr[j] > arr[j + 1]) {
//                swap(&arr[j], &arr[j + 1]);
//            }
//        }
//    }
//}

///**
// * @brief 计算数组的中位数（中值）
// * @param arr 输入数组
// * @param size 数组大小
// * @return 中位数
// */
//uint32_t calculate_median(uint32_t* arr, uint16_t size) {
//		uint32_t* temp;
//    if (size == 0) return 0;
//    
//    // 创建临时数组，避免修改原数组
//    temp = (uint32_t*)malloc(size * sizeof(uint32_t));
//    memcpy(temp, arr, size * sizeof(uint32_t));
//    
//    // 排序临时数组
//    bubble_sort(temp, size);
//    
//    // 计算中位数
//    uint32_t median;
//    if (size % 2 == 0) {
//        // 偶数个元素：取中间两个数的平均值
//        median = (temp[size/2 - 1] + temp[size/2]) / 2;
//    } else {
//        // 奇数个元素：取中间的数
//        median = temp[size/2];
//    }
//    
//    free(temp);
//    return median;
//}

/**
 * @brief 计算滑动窗口中值的平均值（输出单个值）
 * @p * @para msize 数组大小
aram input 输入数组
 * @return 中值的平均值
 */
float median_average(uint32_t* input, uint16_t size) {
    const uint8_t WINDOW_SIZE = 5;
    const uint8_t HALF_WINDOW = WINDOW_SIZE / 2;
    uint32_t window[WINDOW_SIZE];
    uint32_t median_array[10]; // 存储每个窗口的中值
    uint16_t i, j, m, n;
    int16_t idx;
    uint32_t temp;
    float sum = 0;

    // 1. 计算每个窗口的中值，存入median_array
    for (i = 0; i < size; i++) {
        // 填充窗口数据（处理边界）
        for (j = 0; j < WINDOW_SIZE; j++) {
            idx = i + j - HALF_WINDOW;
            idx = (idx < 0) ? 0 : (idx >= size) ? size - 1 : idx;
            window[j] = input[idx];
        }
        // 排序窗口
        for (m = 0; m < WINDOW_SIZE - 1; m++) {
            for (n = 0; n < WINDOW_SIZE - m - 1; n++) {
                if (window[n] > window[n + 1]) {
                    temp = window[n];
                    window[n] = window[n + 1];
                    window[n + 1] = temp;
                }
            }
        }
        median_array[i] = window[HALF_WINDOW]; // 保存单个窗口的中值
    }

    // 2. 计算所有中值的平均值
    for (i = 0; i < size; i++) {
        sum += median_array[i];
    }
    return sum / size;
}

int compare_ints(const void* a, const void* b) {
    int arg1 = *(const int*)a;
    int arg2 = *(const int*)b;
    return (arg1 > arg2) - (arg1 < arg2); // 正确的比较结果返回
}

float median_filter(uint32_t* array, int size) {
    // 有效性检查
	int* temp;
	int i;
	float median;
	
    if (array == NULL || size <= 0) {
        return 0.0f; // 返回默认值或错误代码
    }

    // 创建临时数组用于排序（避免修改原数组）
    temp = (int*)malloc(size * sizeof(int));
    if (!temp) return 0.0f; // 内存分配失败处理

    // 复制数据到临时数组
    for (i = 0; i < size; i++) {
        temp[i] = array[i];
    }

    // 使用标准库排序
    qsort(temp, size, sizeof(int), compare_ints);

    // 计算中位数
//    float median;
    if (size % 2 == 0) {
        // 偶数长度：取中间两个数的平均值
        median = (temp[(size/2)-1] + temp[size/2]) / 2.0f;
    } else {
        // 奇数长度：直接取中间值
        median = temp[size/2];
    }

    // 释放内存
    free(temp);
    return median;
}

// DMA中断服务函数，用于处理ADC数据采集完成后的操作
void DMA2_Stream0_IRQHandler(void) 
{
	int i=0;
	
 uint32_t idex;	//用于将采集到的数据赋值给fft_inputbuf[2*idex]的计数	
//  float bias_voltage2,HZ2,amplitude2,phase2,bias_voltage1,HZ1,amplitude1,phase1;
//	uint8_t temp[40];
//	int i;
	uint16_t   freamplen; // freamp长度的一半
	
	uint8_t n = 5; // 求第n大值
  uint16_t vmax = 0;
  uint16_t vmin = 4095;
//  uint32_t vpp = 0;
  uint32_t temp = 0;
//  uint32_t i = 0;
  uint32_t j = 0;
  uint8_t flag[4096] = {0};
//	arm_cfft_radix4_init_f32(&scfft,FFT_LENGTH,0,1);
	
	float dc_component;
	float fundamental_amp;
	int fundamental_idx;
	float third_harmonic_amp;
  int third_harmonic_idx;
// 定义搜索范围
  uint16_t start_third;
  int end_third;
	float fifth_harmonic_amp ;
  int fifth_harmonic_idx ;
// 定义搜索范围
  int start_fifth;
  int end_fifth;
	float third_ratio;
	float fifth_ratio;
	const float TRIANGLE_THIRD_END =1.0f / 15.0f; // 三角波三次谐波理论比例结束(因为不一定完全重合)
  const float SQUARE_THIRD_END  =1.0f / 5.0f;    // 方波三次谐波理论比例结束(因为不一定完全重合)
   uint32_t peak;
//	
	float max_magnitude;
	int max_index;
	float second_magnitude ;
  int second_index ;
  int exclusion_range ; 
	int detected_count;
	FreqPeak freq1;
	FreqPeak freq2;
	
	    float window, actual_freq_resolution, noise_sum, noise_floor, threshold;
    float half_max, precise_index1, precise_index2;
    float y1, y2, y3, a, b, offset;
    float snr1, snr2, adaptive_min_ratio;
    int noise_count, window_size, is_local_peak;
    int left_bound, right_bound, dynamic_exclusion;
//	
	
	//判断DMA传输完成中断  
	if(DMA_GetITStatus(DMA2_Stream0, DMA_IT_TCIF0))
	{
//		TIM_Cmd(TIM3,DISABLE);                                                                                                                                                                                                                   
		for(i=0;i<4096;i++)	{
			DEBUG_USARTx=USART1;
			printf("{a}%d\r\n",ADC_ConvertedValue[i]);
		}
		
//			for(i=0;i<1024;i++)   //画曲线
//				{ uint32_t pot;
//					
//				    pot=(uint8_t)((ADC_ConvertedValue[2*i]+2*ADC_ConvertedValue[2*i+1]-4344.24f)*60.0f/4468.38f+60+0.5f);
//						printf("add %s,%d,%d\xff\xff\xff","s0.id",0,pot);
//				}
		
		
//		for(i=0;i<4096;i++)	{
//			DEBUG_USARTx=USART2;
//			printf("%d\r\n",(uint16_t)ADC_ConvertedValue[i]);
//		}

//  n = 6; // 求第n大值
//  memset(flag, 0, sizeof(flag));
//  for (i = 1; i <= n; i++)
//  {
//    vmax = 0;
//    for (j = 0; j < 4096; j++)
//    {
//      if (flag[j] == 1)
//        continue;
//      if (vmax < ADC_ConvertedValue[j])
//      {
//        vmax = ADC_ConvertedValue[j];
//        temp = j; // 记录最大值小标
//      }
//    }
//    flag[temp] = 1; // 给已经做过最大的数做标记，在下次搜索时跳过
//  }
//  
//  n = 6; // 求第n小值
//  memset(flag, 0, sizeof(flag));
//  for (i = 1; i <= n; i++)
//  {
//    vmin = 4095;
//    for (j = 0; j < 4096; j++)
//    {
//      if (flag[j] == 1)
//        continue;
//      if (vmin > ADC_ConvertedValue[j])
//      {
//        vmin = ADC_ConvertedValue[j];
//        temp = j;
//      }
//    }
//    flag[temp] = 1;
//  }

//  vpp = vmax - vmin;
	
//	vpp_m=vpp*3.3*1000*100/4095/3.0;
	
//	vpp_array[count++]=vpp;
//	if(count==5) count=0;
//	
//	sum=0;
//	for(i=0;i<5;i++)
//	{
//		sum=sum+vpp_array[i];
//	}
//	
//	vpp_m=(uint32_t)sum/5;
	
//	
//	vpp_fin=median_average(vpp_array,10);
	
//	printf("{b}%.2fmv/r/n",vpp*3.3*1000/4095/3.0);
	
	
	
	
	
																																																																																																																																																																																																																																																																
    // 执行FFT变换
//  for (i = 0; i < 4096; i++)
//  {
//		FFT_InputBuf[2 * i] = (float)ADC_ConvertedValue[i] / 4096.0f * 3.3f; // 实部
//    FFT_InputBuf[2 * i + 1] = 0;                                       // 虚部
//  }

//    arm_cfft_radix4_f32(&scfft, FFT_InputBuf);                  // FFT计算
//    arm_cmplx_mag_f32(FFT_InputBuf, FFT_OutputBuf, 4096); // 取模得幅值

    // 执行FFT变换
    // 变量定义区域 - 放在代码块最上面
    
    // 初始化变量
    window_size = 5;
    noise_sum = 0;
    noise_count = 0;
    
    // 数据预处理和加窗
    for (i = 0; i < 4096; i++)
    {
        // 应用汉宁窗函数减少频谱泄漏
        window = 0.5f * (1.0f - cosf(2.0f * 3.14159f * i / 4095.0f));
        FFT_InputBuf[2 * i] = ((float)ADC_ConvertedValue[i] / 4096.0f * 3.3f) * window; // 实部加窗
        FFT_InputBuf[2 * i + 1] = 0;                                       // 虚部
    }

    arm_cfft_radix4_f32(&scfft, FFT_InputBuf);                  // FFT计算
    arm_cmplx_mag_f32(FFT_InputBuf, FFT_OutputBuf, 4096); // 取模得幅值

    // 修正频率分辨率计算 - 使用实际采样频率
    actual_freq_resolution = (float)fft_sample_freq / FFT_LENGTH;  // 100kHz/4096 = 24.41Hz
    
    // 估计噪声底噪
    for (i = 10; i < FFT_LENGTH/10; i++) {  // 避开DC和高频段
        noise_sum += FFT_OutputBuf[i];
        noise_count++;
    }
    noise_floor = noise_sum / noise_count;
    threshold = noise_floor * 3.0f;  // 3倍噪声阈值

    // 寻找第一个显著峰值（改进算法）
    max_magnitude = 0.0f;
    max_index = 0;

    for (i = window_size; i < FFT_LENGTH/2 - window_size; i++) {
        // 检查是否超过噪声阈值
        if (FFT_OutputBuf[i] < threshold) continue;
        
        // 检查是否为局部最大值
        is_local_peak = 1;
        for (j = -window_size; j <= window_size; j++) {
            if (j != 0 && FFT_OutputBuf[i + j] >= FFT_OutputBuf[i]) {
                is_local_peak = 0;
                break;
            }
        }
        
        if (is_local_peak && FFT_OutputBuf[i] > max_magnitude) {
            max_magnitude = FFT_OutputBuf[i];
            max_index = i;
        }
    }

    // 抛物线插值提高第一个峰值频率精度
    precise_index1 = max_index;
    if (max_index > 0 && max_index < FFT_LENGTH/2 - 1) {
        y1 = FFT_OutputBuf[max_index - 1];
        y2 = FFT_OutputBuf[max_index];
        y3 = FFT_OutputBuf[max_index + 1];
        a = (y1 - 2*y2 + y3) / 2.0f;
        b = (y3 - y1) / 2.0f;
        if (a != 0) {
            offset = -b / (2*a);
            precise_index1 = max_index + offset;
        }
    }

    // 记录第一个峰值
    freq1.magnitude = max_magnitude;
    freq1.bin_index = max_index;
    freq1.frequency = precise_index1 * actual_freq_resolution;  // 使用修正的频率分辨率

    // 计算第一个峰值的半高宽，用于更准确的排除范围
    left_bound = max_index;
    right_bound = max_index;
    half_max = max_magnitude * 0.5f;
    while (left_bound > 1 && FFT_OutputBuf[left_bound] > half_max) left_bound--;
    while (right_bound < FFT_LENGTH/2-1 && FFT_OutputBuf[right_bound] > half_max) right_bound++;
    dynamic_exclusion = (right_bound - left_bound) + 5;  // 动态排除范围

    // 寻找第二个峰值（改进算法）
    second_magnitude = 0.0f;
    second_index = 0;

    for (i = window_size; i < FFT_LENGTH/2 - window_size; i++) {
        // 跳过第一个峰值附近区域（使用动态排除范围）
        if (abs(i - max_index) <= dynamic_exclusion) continue;
        
        // 检查是否超过噪声阈值
        if (FFT_OutputBuf[i] < threshold) continue;
        
        // 检查是否为局部最大值
        is_local_peak = 1;
        for (j = -window_size; j <= window_size; j++) {
            if (j != 0 && FFT_OutputBuf[i + j] >= FFT_OutputBuf[i]) {
                is_local_peak = 0;
                break;
            }
        }
        
        if (is_local_peak && FFT_OutputBuf[i] > second_magnitude) {
            second_magnitude = FFT_OutputBuf[i];
            second_index = i;
        }
    }

    // 抛物线插值提高第二个峰值频率精度
    precise_index2 = second_index;
    if (second_index > 0 && second_index < FFT_LENGTH/2 - 1) {
        y1 = FFT_OutputBuf[second_index - 1];
        y2 = FFT_OutputBuf[second_index];
        y3 = FFT_OutputBuf[second_index + 1];
        a = (y1 - 2*y2 + y3) / 2.0f;
        b = (y3 - y1) / 2.0f;
        if (a != 0) {
            offset = -b / (2*a);
            precise_index2 = second_index + offset;
        }
    }

    // 计算信噪比进行质量评估
    snr1 = max_magnitude / noise_floor;
    snr2 = second_magnitude / noise_floor;
    
    // 更严格的第二峰值检测条件
    adaptive_min_ratio = 0.15f;  // 基础最小比例
    if (snr1 > 10.0f) adaptive_min_ratio = 0.1f;  // 主峰信噪比高时，降低要求
    if (snr1 < 5.0f) adaptive_min_ratio = 0.25f;  // 主峰信噪比低时，提高要求

    // 检查第二个峰值是否足够显著
    if (second_magnitude < max_magnitude * adaptive_min_ratio || snr2 < 3.0f) {
        detected_count = 1;  // 只检测到一个显著频率
    } else {
        detected_count = 2;  // 成功检测到两个频率
    }

    // 记录第二个峰值
    freq2.magnitude = second_magnitude;
    freq2.bin_index = second_index;
    freq2.frequency = precise_index2 * actual_freq_resolution;  // 使用修正的频率分辨率

    // 输出结果（增加更多诊断信息）
    if (detected_count >= 1) {
        printf("主频率: %.2f Hz, 幅度: %.2f, SNR: %.1f dB\r\n", 
               freq1.frequency, freq1.magnitude, 20*log10f(snr1));
    }
    if (detected_count == 2) {
        printf("次频率: %.2f Hz, 幅度: %.2f, SNR: %.1f dB\r\n", 
               freq2.frequency, freq2.magnitude, 20*log10f(snr2));
        printf("频率分离成功！比例: %.3f\r\n", second_magnitude/max_magnitude);
    } else {
        printf("仅检测到单一显著频率，噪声底噪: %.2f\r\n", noise_floor);
    }
	
	
	
  
    // 直流分量
    dc_component = FFT_OutputBuf[0];

    // 1. 寻找基频（最大峰值）
    fundamental_amp = 0.0f;
    fundamental_idx = 0;
		
    for (i = 1; i < 4096 / 2; i++)
    {
        if (FFT_OutputBuf[i] > fundamental_amp)
        {
            fundamental_amp = FFT_OutputBuf[i];
            fundamental_idx = i;
        }
    }

    // 2. 寻找三次谐波（在2*fundamental_idx到4*fundamental_idx之间）
    third_harmonic_amp = 0.0f;
    third_harmonic_idx = 0;

    // 定义搜索范围
    start_third = 2 * fundamental_idx;
    end_third = 4 * fundamental_idx;

    // 限制搜索范围在有效FFT区间内
    if (end_third > 4096 / 2)
        end_third = 4096/ 2;

    // 在三次谐波预期区域寻找峰值
    for (i = start_third; i < end_third; i++)
    {
        if (FFT_OutputBuf[i] > third_harmonic_amp)
        {
            third_harmonic_amp = FFT_OutputBuf[i];
            third_harmonic_idx = i;
        }
    }

    // 3. 寻找五次谐波（在4*fundamental_idx到6*fundamental_idx之间）
    fifth_harmonic_amp = 0.0f;
    fifth_harmonic_idx = 0;

    // 定义搜索范围
    start_fifth = 4 * fundamental_idx;
    end_fifth = 6 * fundamental_idx;

    // 限制搜索范围在有效FFT区间内
    if (end_fifth > 4096 / 2)
        end_fifth = 4096 / 2;

    // 在五次谐波预期区域寻找峰值
    for (i = start_fifth; i < end_fifth; i++)
    {
        if (FFT_OutputBuf[i] > fifth_harmonic_amp)
        {
            fifth_harmonic_amp = FFT_OutputBuf[i];
            fifth_harmonic_idx = i;
        }
    }

    // 检查找到的谐波是否有意义（幅度至少为基频的5%）
    if (third_harmonic_amp < fundamental_amp * 0.05f)
    {
        third_harmonic_amp = 0.0f;
    }

    if (fifth_harmonic_amp < fundamental_amp * 0.05f)
    {
        fifth_harmonic_amp = 0.0f;
    }

    // 计算谐波比例
    third_ratio = (third_harmonic_amp > 0) ? (third_harmonic_amp / fundamental_amp) : 0;
    fifth_ratio = (fifth_harmonic_amp > 0) ? (fifth_harmonic_amp / fundamental_amp) : 0;

    // 4. 波形判断

    // 如果没有明显谐波，是正弦波
    if (third_ratio < 0.05f && fifth_ratio < 0.05f)
    {
				DEBUG_USARTx=USART1;
//        printf("t5.txt=\"正弦波\"\xff\xff\xff");
//			        printf("正弦波/r/n");
    }

    if (third_ratio > SQUARE_THIRD_END)
    {
			DEBUG_USARTx=USART1;
//       printf("t5.txt=\"方波\"\xff\xff\xff");
//			       printf("方波/r/n");
				vpp_m-=6;
    }
    else if (third_ratio > TRIANGLE_THIRD_END)
    {
			DEBUG_USARTx=USART1;
//        printf("t5.txt=\"三角波\"\xff\xff\xff");
//						       printf("三角波/r/n");
				vpp_m+=4;
    }

			
//		if(vpp_m<=300)
//		vpp_m= (uint32_t)pow(vpp_m/0.798, 1.0033);
//	else if(vpp_m<550)
//		vpp_m=(uint32_t)pow(vpp_m/0.791, 1.0015);
//	else if(vpp_m<=617)
//		vpp_m=(uint32_t)pow(vpp_m/0.788,1.0032);
//	else if (vpp_m)
//		vpp_m=(uint32_t)pow(vpp_m/0.788,1.005);
//	if(vpp_m<210&&vpp_m>58)
//		vpp_m-=4;
//	else if(vpp_m>=210&&vpp_m<500)
//		vpp_m-=2;
//	else if(vpp_m>850)
//		vpp_m+=2;
//	if(vpp_m>940)
//		vpp_m+=2;
	
//	if(Freq>9980&&Freq<=20100)
//		vpp_m-=3;
//	else if(Freq>20100&&Freq<=30100)
//		vpp_m-=6;
//	else if(Freq>30100&&Freq<=40100)
//		vpp_m-=6;
//	else if(Freq>40100&&Freq<=50100)
//		vpp_m-=9;
//	else if(Freq>50100&&Freq<=60100)
//		vpp_m-=9;
//	else if(Freq>60100&&Freq<=70100)
//		vpp_m-=12;
//	else if(Freq>70100&&Freq<=80100)
//		vpp_m-=12;
//	else if(Freq>80100&&Freq<=90100)
//		vpp_m-=15;
//	else if(Freq>90100&&Freq<=100100)
//		vpp_m-=18;
	
//	if(Freq>9980&&Freq<=20100)
//		vpp_m=vpp_m*0.96;
//	else if(Freq>20100&&Freq<=30100)
//		vpp_m=vpp_m*0.96;
//	else if(Freq>30100&&Freq<=40100)
//		vpp_m=vpp_m;
//	else if(Freq>40100&&Freq<=50100)
//		vpp_m=vpp_m;
//	else if(Freq>50100&&Freq<=60100)
//		vpp_m=vpp_m*0.94;
//	else if(Freq>60100&&Freq<=70100)
//		vpp_m=vpp_m*0.94;
//	else if(Freq>70100&&Freq<=80100)
//		vpp_m=vpp_m*0.94;
//	else if(Freq>80100&&Freq<=90100)
//		vpp_m=vpp_m*0.87;
//	else if(Freq>90100&&Freq<=100100)
//		vpp_m=vpp_m*0.94;
	
		DEBUG_USARTx=USART1;
	
	
	
//	printf("x1.val=%d\xff\xff\xff",vpp_m*100);

//		printf("{b}%.2fmv\r\n",calculate_peak_avg(ADC_ConvertedValue, 100)*1000*0.9*3.3f/4096);
		
//		printf("{b}%.2fmv\r\n",vpp*1000*0.9*3.3f/4096);
		
//		printf("x1.val=%d\xff\xff\xff",(uint32_t)vpp*3.3*1000/4095/3.0);
		
		TIM_Cmd(TIM3,ENABLE); 
		
		DMA_ClearITPendingBit(DMA2_Stream0, DMA_IT_TCIF0);
	}
}

void Rheostat_Init(void)
{
	Rheostat_ADC_GPIO_Config(); 
	Rheostat_ADC_Mode_Config();
	arm_cfft_radix4_init_f32(&scfft,FFT_LENGTH,0,1);
	Tim3_Init(arrv-1,pscv-1);
}
