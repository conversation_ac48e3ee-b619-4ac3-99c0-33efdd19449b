/**
  ******************************************************************************
  * @file    bsp_bsp_adc.c
  * <AUTHOR>
  * @version V1.0
  * @date    2015-xx-xx
  * @brief   adc����
  ******************************************************************************
  * @attention
  *
  * ʵ��ƽ̨:Ұ��  STM32 F429 ������  
  * ��̳    :http://www.firebbs.cn
  * �Ա�    :https://fire-stm32.taobao.com
  *
  ******************************************************************************
  */ 

#include "./adc/bsp_adc.h"
#include "arm_math.h"
#include "usart.h"
#include <math.h>
#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h> 
#include "stm32f4xx_it.h"
//#include "amplitude_calibration.h"

//#define sampledot  4096
#define FFT_LENGTH		4096		//4096��FFT
#define fft_arr 10     // ���ڼ���FFT����Ƶ�ʵ�ϵ��             
#define fft_psc 90       // ���ڼ���FFT����Ƶ�ʵ�ϵ��     
#define ADC_ARRAY_SIZE 4096  // ADC����̶���С
#define DEFAULT_FILTER_WINDOW 5  // Ĭ����ֵ�˲����ڴ�С��������

#define SAMPLE_RATE 8192      // ������Hz
#define MIN_PEAK_RATIO 0.1f   // ��С��ֵ����
#define FREQ_RESOLUTION ((float)SAMPLE_RATE / FFT_LENGTH)  // ��̬Ƶ�ʷֱ���

typedef struct {
    float frequency;          // Ƶ��ֵ
    float magnitude;         // ����ֵ
    int bin_index;           // Ƶ��bin����
} FreqPeak;

uint32_t filtered_data[ADC_ARRAY_SIZE];
 float bias_voltage2,HZ2,amplitude2,phase2,bias_voltage1,HZ1,amplitude1,phase1;

const uint32_t fft_sample_freq=90000000/(fft_arr*fft_psc);  // ����FFT����Ƶ��:100khz
extern USART_TypeDef * DEBUG_USARTx;  

uint16_t arrv=30;
uint16_t pscv=30;

uint32_t vpp_array[5];
uint32_t vpp_m;
float vpp_max;
uint32_t sum;

uint8_t count;

float FFT_InputBuf[FFT_LENGTH*2];	 // FFT�������飬���ڴ�Ÿ���
float FFT_OutputBuf[FFT_LENGTH];	 // FFT������飬��ŷ�ֵ
arm_cfft_radix4_instance_f32 scfft;   // FFTʵ���ṹ��
//uint32_t sampledata[sampledot]={0};//���ڴ��ADC�������ݵ�����,��16λ����adc2 pa5�� ��16λ����adc1 pa6

float phase_difference=0; // ���ڴ����λ��ı���
float freamp[50];//���ڴ�Ÿ���г��Ƶ�ʺͷ�ֵ������

__IO uint32_t ADC_ConvertedValue[4096];
__IO uint32_t ADC_ConvertedValuelocal[8192];

uint16_t vpp;
float32_t maxvalue;
uint32_t max_pos;
float vpp_fin;

extern uint32_t Freq;


// ��ʱ��3��ʼ������
void Tim3_Init(uint16_t arr,uint16_t psc)  //TIM3��ʱ�����ʱ��Ƶ��Ϊ90M
{
	TIM_TimeBaseInitTypeDef   TIM_TimeBaseInitstruct;          
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3,ENABLE);          
  
	TIM_TimeBaseInitstruct.TIM_Period=arr;   
  TIM_TimeBaseInitstruct.TIM_Prescaler=psc;
	TIM_TimeBaseInitstruct.TIM_CounterMode=TIM_CounterMode_Up;
	TIM_TimeBaseInitstruct.TIM_ClockDivision=TIM_CKD_DIV1;
	TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitstruct);
	
	//TIM_ITConfig(TIM3,TIM_IT_Update,ENABLE);     
	TIM_SelectOutputTrigger(TIM3, TIM_TRGOSource_Update);	
//	TIM_Cmd(TIM3,DISABLE);
	TIM_Cmd(TIM3,ENABLE);

}

static void Rheostat_ADC_GPIO_Config(void)
{
		GPIO_InitTypeDef GPIO_InitStructure;
	
	// ʹ�� GPIO ʱ��
	RCC_AHB1PeriphClockCmd(RHEOSTAT_ADC_GPIO_CLK1, ENABLE);
//	RCC_AHB1PeriphClockCmd(RHEOSTAT_ADC_GPIO_CLK2, ENABLE);
		
	// ���� IO
	GPIO_InitStructure.GPIO_Pin = RHEOSTAT_ADC_GPIO_PIN1;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;	    
  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL ; //������������
	GPIO_Init(RHEOSTAT_ADC_GPIO_PORT1, &GPIO_InitStructure);

//	// ���� IO
//	GPIO_InitStructure.GPIO_Pin = RHEOSTAT_ADC_GPIO_PIN2;
//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;	    
//  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL ; //������������
//	GPIO_Init(RHEOSTAT_ADC_GPIO_PORT2, &GPIO_InitStructure);	
}

static void Rheostat_ADC_Mode_Config(void)
{
	uint32_t index=1;
	
	DMA_InitTypeDef DMA_InitStructure;
	ADC_InitTypeDef ADC_InitStructure;
  ADC_CommonInitTypeDef ADC_CommonInitStructure;
	NVIC_InitTypeDef NVIC_InitStructure;
	
  // ------------------DMA Init �ṹ����� ��ʼ��--------------------------
  // ADC1ʹ��DMA2��������0��ͨ��0��������ֲ�̶�����
  // ����DMAʱ��
  RCC_AHB1PeriphClockCmd(RHEOSTAT_ADC_DMA_CLK, ENABLE); 
	// �����ַΪ��ADC ���ݼĴ�����ַ
	DMA_InitStructure.DMA_PeripheralBaseAddr = RHEOSTAT_ADC_CDR_ADDR;	
  // �洢����ַ��ʵ���Ͼ���һ���ڲ�SRAM�ı���	
	DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)ADC_ConvertedValue;  
  // ���ݴ��䷽��Ϊ���赽�洢��	
	DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;	
	// ��������СΪ��ָһ�δ����������
	DMA_InitStructure.DMA_BufferSize = 4096;	
	// ����Ĵ���ֻ��һ������ַ���õ���
	DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
  // �洢����ַ�̶�
	DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable; 
  // // �������ݴ�СΪ���֣��������ֽ� 
	DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_Word; 
  //	�洢�����ݴ�СҲΪ���֣����������ݴ�С��ͬ
	DMA_InitStructure.DMA_MemoryDataSize = DMA_PeripheralDataSize_Word;	
	// ѭ������ģʽ
	DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
  // DMA ����ͨ�����ȼ�Ϊ�ߣ���ʹ��һ��DMAͨ��ʱ�����ȼ����ò�Ӱ��
	DMA_InitStructure.DMA_Priority = DMA_Priority_High;
  // ��ֹDMA FIFO	��ʹ��ֱ��ģʽ
  DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;  
  // FIFO ��С��FIFOģʽ��ֹʱ�������������	
  DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
  DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
  DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;  
	// ѡ�� DMA ͨ����ͨ������������
  DMA_InitStructure.DMA_Channel = RHEOSTAT_ADC_DMA_CHANNEL; 
  //��ʼ��DMA�������൱��һ����Ĺܵ����ܵ������кܶ�ͨ��
	DMA_Init(RHEOSTAT_ADC_DMA_STREAM, &DMA_InitStructure);
	// ʹ��DMA��
	DMA_ITConfig(RHEOSTAT_ADC_DMA_STREAM, DMA_IT_TC, ENABLE);
	
  DMA_Cmd(RHEOSTAT_ADC_DMA_STREAM, ENABLE);
	
	NVIC_InitStructure.NVIC_IRQChannel = DMA2_Stream0_IRQn;  //DMA2_Stream0�ж�
  NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority=3;  //��ռ���ȼ�1
  NVIC_InitStructure.NVIC_IRQChannelSubPriority =2;        //�����ȼ�1
  NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;            //IRQͨ��ʹ��
  NVIC_Init(&NVIC_InitStructure);
	
	
	// ����ADCʱ��
	RCC_APB2PeriphClockCmd(RHEOSTAT_ADC1_CLK , ENABLE);
//  RCC_APB2PeriphClockCmd(RHEOSTAT_ADC2_CLK , ENABLE);
	
  // -------------------ADC Common �ṹ�� ���� ��ʼ��------------------------
	// ����ADCģʽ
  ADC_CommonInitStructure.ADC_Mode = ADC_DualMode_RegSimult;
  // ʱ��Ϊfpclk x��Ƶ	
  ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;
  // ��ֹDMAֱ�ӷ���ģʽ	
  ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_1;
  // ����ʱ����	
  ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles;  
  ADC_CommonInit(&ADC_CommonInitStructure);
	
  // -------------------ADC Init �ṹ�� ���� ��ʼ��--------------------------
	ADC_StructInit(&ADC_InitStructure);
  // ADC �ֱ���
  ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;
  // ��ֹɨ��ģʽ����ͨ���ɼ�����Ҫ	
  ADC_InitStructure.ADC_ScanConvMode = DISABLE; 
  // ����ת��	
  ADC_InitStructure.ADC_ContinuousConvMode = ENABLE; 
  //��ֹ�ⲿ���ش���
  ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;
  //�ⲿ����ͨ����������ʹ��������������ֵ��㸳ֵ����
  ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T3_TRGO;
  //�����Ҷ���	
  ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
  //ת��ͨ�� 1��
  ADC_InitStructure.ADC_NbrOfConversion = 1;
	
  //---------------------------------------------------------------------------	
  ADC_Init(RHEOSTAT_ADC1, &ADC_InitStructure);	
   // ���� ADC ͨ��ת��˳��Ϊ1����һ��ת��������ʱ��Ϊ3��ʱ������
  ADC_RegularChannelConfig(RHEOSTAT_ADC1, RHEOSTAT_ADC_CHANNEL1, 1, ADC_SampleTime_3Cycles);   
  //---------------------------------------------------------------------------
//	ADC_ExternalTrigConvCmd(ADC1,ENABLE);
//	ADC_Init(RHEOSTAT_ADC2, &ADC_InitStructure);
//  // ���� ADC ͨ��ת��˳��Ϊ1����һ��ת��������ʱ��Ϊ3��ʱ������
//  ADC_RegularChannelConfig(RHEOSTAT_ADC2, RHEOSTAT_ADC_CHANNEL2, 1, ADC_SampleTime_3Cycles);   
  //---------------------------------------------------------------------------
		
  // ʹ��DMA���� after last transfer (multi-ADC mode)
//  ADC_MultiModeDMARequestAfterLastTransferCmd(ENABLE);
// ʹ��ADC DMA
  ADC_DMACmd(RHEOSTAT_ADC1, ENABLE);
	
  // ʹ��ADC
  ADC_Cmd(RHEOSTAT_ADC1, ENABLE);  
//  ADC_Cmd(RHEOSTAT_ADC2, ENABLE);   
  //��ʼadcת������������
//  ADC_SoftwareStartConv(RHEOSTAT_ADC1);

//  ADC_SoftwareStartConv(RHEOSTAT_ADC2);
}

int fft_getpeak(float *inputx,float *input,float *output,uint16_t inlen,uint8_t x,uint8_t N,float y) //  intlen �������鳤�ȣ�xѰ�ҳ���
{                                                                           
	int i,i2;
	uint32_t idex;  //��ͬ����һ�������еģ���Ϊ�����ڲ�ͬ�ĺ����б�����
	float datas;
	float sum;
	int outlen=0;
	for(i=0;i<inlen-x;i+=x)
	{
		arm_max_f32(input+i,x,&datas,&idex);   
		if( (input[i+idex]>=input[i+idex+1])&&(input[i+idex]>=input[i+idex-1])&&( (2*datas) /FFT_LENGTH )>y)   
		   {
			   sum=0;   
			   for(i2=i+idex-N;i2<i+idex+N;i2++)   
			   {
				   sum+=input[i2];          
			   }        
			   if(1.5f*sum/(2*N)<datas)       
			   {                                                                                             
				     output[3*outlen+2] = atan2(inputx[2*(i+idex+1)+1],inputx[2*(i+idex+1)])*180/3.1415926f;	//������λ		   
				     output[3*outlen+1] = 1.0f*(2*datas)/FFT_LENGTH;   //�������
					   output[3*outlen] = 1.0*fft_sample_freq*(i+idex+1)/FFT_LENGTH;//����Ƶ��
					   outlen++;				   
			   }                                                                                               
               else continue;			   
		   }
			
		else continue;
		
	}
	return outlen;
	
	
}

float calculate_peak_avg(volatile uint32_t* adc_data, int sample_times) {
		uint16_t cnt;
    uint8_t flag[1024];  // ������飨��ԭʼ����һ�£�
    uint16_t i, j, temp;
    float vmax, vmin;
    float sum_vpp = 0.0f;   // ���ֵ�ۼӺ�

    for (cnt = 0; cnt < sample_times; cnt++) {
        // �����N��ֵ������ԭʼ�߼���
        memset(flag, 0, sizeof(flag));
        for (i = 1; i <= 5; i++) {
            vmax = 0.0;
            for (j = 0; j < 1024; j++) {
                if (flag[j] == 1) continue;
                if (vmax < adc_data[j]) {
                    vmax = adc_data[j];
                    temp = j;
                }
            }
            flag[temp] = 1;
        }

        // �����NСֵ������ԭʼ�߼���
        memset(flag, 0, sizeof(flag));
        for (i = 1; i <= 5; i++) {
            vmin = 4096.0;
            for (j = 0; j < 1024; j++) {
                if (flag[j] == 1) continue;
                if (vmin > adc_data[j]) {
                    vmin = adc_data[j];
                    temp = j;
                }
            }
            flag[temp] = 1;
        }

        sum_vpp += (vmax - vmin);  // �ۼӵ�ǰ���ֵ
    }

    return sum_vpp / sample_times;  // ���ؾ�ֵ
}

//static void swap(uint32_t* a, uint32_t* b) {
//    uint32_t temp = *a;
//    *a = *b;
//    *b = temp;
//}

///**
// * @brief ð������С���������ã�
// */
//static void bubble_sort(uint32_t* arr, uint16_t size) {
//    for (uint16_t i = 0; i < size - 1; i++) {
//        for (uint16_t j = 0; j < size - i - 1; j++) {
//            if (arr[j] > arr[j + 1]) {
//                swap(&arr[j], &arr[j + 1]);
//            }
//        }
//    }
//}

///**
// * @brief �����������λ������ֵ��
// * @param arr ��������
// * @param size �����С
// * @return ��λ��
// */
//uint32_t calculate_median(uint32_t* arr, uint16_t size) {
//		uint32_t* temp;
//    if (size == 0) return 0;
//    
//    // ������ʱ���飬�����޸�ԭ����
//    temp = (uint32_t*)malloc(size * sizeof(uint32_t));
//    memcpy(temp, arr, size * sizeof(uint32_t));
//    
//    // ������ʱ����
//    bubble_sort(temp, size);
//    
//    // ������λ��
//    uint32_t median;
//    if (size % 2 == 0) {
//        // ż����Ԫ�أ�ȡ�м���������ƽ��ֵ
//        median = (temp[size/2 - 1] + temp[size/2]) / 2;
//    } else {
//        // ������Ԫ�أ�ȡ�м����
//        median = temp[size/2];
//    }
//    
//    free(temp);
//    return median;
//}

/**
 * @brief ���㻬��������ֵ��ƽ��ֵ���������ֵ��
 * @p * @para msize �����С
aram input ��������
 * @return ��ֵ��ƽ��ֵ
 */
float median_average(uint32_t* input, uint16_t size) {
    const uint8_t WINDOW_SIZE = 5;
    const uint8_t HALF_WINDOW = WINDOW_SIZE / 2;
    uint32_t window[WINDOW_SIZE];
    uint32_t median_array[10]; // �洢ÿ�����ڵ���ֵ
    uint16_t i, j, m, n;
    int16_t idx;
    uint32_t temp;
    float sum = 0;

    // 1. ����ÿ�����ڵ���ֵ������median_array
    for (i = 0; i < size; i++) {
        // ��䴰�����ݣ������߽磩
        for (j = 0; j < WINDOW_SIZE; j++) {
            idx = i + j - HALF_WINDOW;
            idx = (idx < 0) ? 0 : (idx >= size) ? size - 1 : idx;
            window[j] = input[idx];
        }
        // ���򴰿�
        for (m = 0; m < WINDOW_SIZE - 1; m++) {
            for (n = 0; n < WINDOW_SIZE - m - 1; n++) {
                if (window[n] > window[n + 1]) {
                    temp = window[n];
                    window[n] = window[n + 1];
                    window[n + 1] = temp;
                }
            }
        }
        median_array[i] = window[HALF_WINDOW]; // ���浥�����ڵ���ֵ
    }

    // 2. ����������ֵ��ƽ��ֵ
    for (i = 0; i < size; i++) {
        sum += median_array[i];
    }
    return sum / size;
}

int compare_ints(const void* a, const void* b) {
    int arg1 = *(const int*)a;
    int arg2 = *(const int*)b;
    return (arg1 > arg2) - (arg1 < arg2); // ��ȷ�ıȽϽ������
}

float median_filter(uint32_t* array, int size) {
    // ��Ч�Լ��
	int* temp;
	int i;
	float median;
	
    if (array == NULL || size <= 0) {
        return 0.0f; // ����Ĭ��ֵ��������
    }

    // ������ʱ�����������򣨱����޸�ԭ���飩
    temp = (int*)malloc(size * sizeof(int));
    if (!temp) return 0.0f; // �ڴ����ʧ�ܴ���

    // �������ݵ���ʱ����
    for (i = 0; i < size; i++) {
        temp[i] = array[i];
    }

    // ʹ�ñ�׼������
    qsort(temp, size, sizeof(int), compare_ints);

    // ������λ��
//    float median;
    if (size % 2 == 0) {
        // ż�����ȣ�ȡ�м���������ƽ��ֵ
        median = (temp[(size/2)-1] + temp[size/2]) / 2.0f;
    } else {
        // �������ȣ�ֱ��ȡ�м�ֵ
        median = temp[size/2];
    }

    // �ͷ��ڴ�
    free(temp);
    return median;
}

// DMA�жϷ����������ڴ���ADC���ݲɼ���ɺ�Ĳ���
void DMA2_Stream0_IRQHandler(void) 
{
	int i=0;
	
 uint32_t idex;	//���ڽ��ɼ��������ݸ�ֵ��fft_inputbuf[2*idex]�ļ���	
//  float bias_voltage2,HZ2,amplitude2,phase2,bias_voltage1,HZ1,amplitude1,phase1;
//	uint8_t temp[40];
//	int i;
	uint16_t   freamplen; // freamp���ȵ�һ��
	
	uint8_t n = 5; // ���n��ֵ
  uint16_t vmax = 0;
  uint16_t vmin = 4095;
//  uint32_t vpp = 0;
  uint32_t temp = 0;
//  uint32_t i = 0;
  uint32_t j = 0;
  uint8_t flag[4096] = {0};
//	arm_cfft_radix4_init_f32(&scfft,FFT_LENGTH,0,1);
	
	float dc_component;
	float fundamental_amp;
	int fundamental_idx;
	float third_harmonic_amp;
  int third_harmonic_idx;
// ����������Χ
  uint16_t start_third;
  int end_third;
	float fifth_harmonic_amp ;
  int fifth_harmonic_idx ;
// ����������Χ
  int start_fifth;
  int end_fifth;
	float third_ratio;
	float fifth_ratio;
	const float TRIANGLE_THIRD_END =1.0f / 15.0f; // ���ǲ�����г�����۱�������(��Ϊ��һ����ȫ�غ�)
  const float SQUARE_THIRD_END  =1.0f / 5.0f;    // ��������г�����۱�������(��Ϊ��һ����ȫ�غ�)
   uint32_t peak;
//	
	float max_magnitude;
	int max_index;
	float second_magnitude ;
  int second_index ;
  int exclusion_range ; 
	int detected_count;
	FreqPeak freq1;
	FreqPeak freq2;
//	
	
	//�ж�DMA��������ж�  
	if(DMA_GetITStatus(DMA2_Stream0, DMA_IT_TCIF0))
	{
//		TIM_Cmd(TIM3,DISABLE);                                                                                                                                                                                                                   
		for(i=0;i<4096;i++)	{
			DEBUG_USARTx=USART1;
			printf("{a}%d\r\n",ADC_ConvertedValue[i]);
		}
		
//			for(i=0;i<1024;i++)   //������
//				{ uint32_t pot;
//					
//				    pot=(uint8_t)((ADC_ConvertedValue[2*i]+2*ADC_ConvertedValue[2*i+1]-4344.24f)*60.0f/4468.38f+60+0.5f);
//						printf("add %s,%d,%d\xff\xff\xff","s0.id",0,pot);
//				}
		
		
//		for(i=0;i<4096;i++)	{
//			DEBUG_USARTx=USART2;
//			printf("%d\r\n",(uint16_t)ADC_ConvertedValue[i]);
//		}

//  n = 6; // ���n��ֵ
//  memset(flag, 0, sizeof(flag));
//  for (i = 1; i <= n; i++)
//  {
//    vmax = 0;
//    for (j = 0; j < 4096; j++)
//    {
//      if (flag[j] == 1)
//        continue;
//      if (vmax < ADC_ConvertedValue[j])
//      {
//        vmax = ADC_ConvertedValue[j];
//        temp = j; // ��¼���ֵС��
//      }
//    }
//    flag[temp] = 1; // ���Ѿ���������������ǣ����´�����ʱ����
//  }
//  
//  n = 6; // ���nСֵ
//  memset(flag, 0, sizeof(flag));
//  for (i = 1; i <= n; i++)
//  {
//    vmin = 4095;
//    for (j = 0; j < 4096; j++)
//    {
//      if (flag[j] == 1)
//        continue;
//      if (vmin > ADC_ConvertedValue[j])
//      {
//        vmin = ADC_ConvertedValue[j];
//        temp = j;
//      }
//    }
//    flag[temp] = 1;
//  }

//  vpp = vmax - vmin;
	
//	vpp_m=vpp*3.3*1000*100/4095/3.0;
	
//	vpp_array[count++]=vpp;
//	if(count==5) count=0;
//	
//	sum=0;
//	for(i=0;i<5;i++)
//	{
//		sum=sum+vpp_array[i];
//	}
//	
//	vpp_m=(uint32_t)sum/5;
	
//	
//	vpp_fin=median_average(vpp_array,10);
	
//	printf("{b}%.2fmv/r/n",vpp*3.3*1000/4095/3.0);
	
	
	
	
	
																																																																																																																																																																																																																																																																
    // ִ��FFT�任
  for (i = 0; i < 4096; i++)
  {
		FFT_InputBuf[2 * i] = (float)ADC_ConvertedValue[i] / 4096.0f * 3.3f; // ʵ��
    FFT_InputBuf[2 * i + 1] = 0;                                       // �鲿
  }

    arm_cfft_radix4_f32(&scfft, FFT_InputBuf);                  // FFT����
    arm_cmplx_mag_f32(FFT_InputBuf, FFT_OutputBuf, 4096); // ȡģ�÷�ֵ

	// Ѱ��ȫ������ֵ
    max_magnitude = 0.0f;
    max_index = 0;

    for (i = 1; i < FFT_LENGTH/2; i++) {  // ֻ������ЧƵ�ʷ�Χ
       if (FFT_OutputBuf[i] > max_magnitude) {
        max_magnitude = FFT_OutputBuf[i];
        max_index = i;
    }
}

// ��¼��һ����ֵ

freq1.magnitude = max_magnitude;
freq1.bin_index = max_index;
freq1.frequency = max_index * FREQ_RESOLUTION;

// Ѱ�ҵڶ�����ֵ���ܿ���һ����ֵ������
second_magnitude = 0.0f;
second_index = 0;
exclusion_range = 3;  // �ų����帽��3��bin

for (i = 1; i < FFT_LENGTH/2; i++) {
    // �������帽������
    if (abs(i - max_index) <= exclusion_range) continue;
    
    if (FFT_OutputBuf[i] > second_magnitude) {
        second_magnitude = FFT_OutputBuf[i];
        second_index = i;
    }
}

// ���ڶ�����ֵ�Ƿ��㹻����
if (second_magnitude < max_magnitude * MIN_PEAK_RATIO) {
    detected_count = 1;  // ֻ��⵽һ������Ƶ��
} else {
    detected_count = 2;  // �ɹ���⵽����Ƶ��
}

// ��¼�ڶ�����ֵ
freq2.magnitude = second_magnitude;
freq2.bin_index = second_index;
freq2.frequency = second_index * FREQ_RESOLUTION;

// ������
if (detected_count >= 1) {
    printf("��Ƶ��: %.2f Hz, ����: %.2f\r\n", freq1.frequency, freq1.magnitude);
}
if (detected_count == 2) {
    printf("��Ƶ��: %.2f Hz, ����: %.2f\r\n", freq2.frequency, freq2.magnitude);
    printf("Ƶ�ʷ���ɹ���\r\n");
} else {
    printf("����⵽��һ����Ƶ��\r\n");
}
	
	
	
  
    // ֱ������
    dc_component = FFT_OutputBuf[0];

    // 1. Ѱ�һ�Ƶ������ֵ��
    fundamental_amp = 0.0f;
    fundamental_idx = 0;
		
    for (i = 1; i < 4096 / 2; i++)
    {
        if (FFT_OutputBuf[i] > fundamental_amp)
        {
            fundamental_amp = FFT_OutputBuf[i];
            fundamental_idx = i;
        }
    }

    // 2. Ѱ������г������2*fundamental_idx��4*fundamental_idx֮�䣩
    third_harmonic_amp = 0.0f;
    third_harmonic_idx = 0;

    // ����������Χ
    start_third = 2 * fundamental_idx;
    end_third = 4 * fundamental_idx;

    // ����������Χ����ЧFFT������
    if (end_third > 4096 / 2)
        end_third = 4096/ 2;

    // ������г��Ԥ������Ѱ�ҷ�ֵ
    for (i = start_third; i < end_third; i++)
    {
        if (FFT_OutputBuf[i] > third_harmonic_amp)
        {
            third_harmonic_amp = FFT_OutputBuf[i];
            third_harmonic_idx = i;
        }
    }

    // 3. Ѱ�����г������4*fundamental_idx��6*fundamental_idx֮�䣩
    fifth_harmonic_amp = 0.0f;
    fifth_harmonic_idx = 0;

    // ����������Χ
    start_fifth = 4 * fundamental_idx;
    end_fifth = 6 * fundamental_idx;

    // ����������Χ����ЧFFT������
    if (end_fifth > 4096 / 2)
        end_fifth = 4096 / 2;

    // �����г��Ԥ������Ѱ�ҷ�ֵ
    for (i = start_fifth; i < end_fifth; i++)
    {
        if (FFT_OutputBuf[i] > fifth_harmonic_amp)
        {
            fifth_harmonic_amp = FFT_OutputBuf[i];
            fifth_harmonic_idx = i;
        }
    }

    // ����ҵ���г���Ƿ������壨��������Ϊ��Ƶ��5%��
    if (third_harmonic_amp < fundamental_amp * 0.05f)
    {
        third_harmonic_amp = 0.0f;
    }

    if (fifth_harmonic_amp < fundamental_amp * 0.05f)
    {
        fifth_harmonic_amp = 0.0f;
    }

    // ����г������
    third_ratio = (third_harmonic_amp > 0) ? (third_harmonic_amp / fundamental_amp) : 0;
    fifth_ratio = (fifth_harmonic_amp > 0) ? (fifth_harmonic_amp / fundamental_amp) : 0;

    // 4. �����ж�

    // ���û������г���������Ҳ�
    if (third_ratio < 0.05f && fifth_ratio < 0.05f)
    {
				DEBUG_USARTx=USART1;
//        printf("t5.txt=\"���Ҳ�\"\xff\xff\xff");
//			        printf("���Ҳ�/r/n");
    }

    if (third_ratio > SQUARE_THIRD_END)
    {
			DEBUG_USARTx=USART1;
//       printf("t5.txt=\"����\"\xff\xff\xff");
//			       printf("����/r/n");
				vpp_m-=6;
    }
    else if (third_ratio > TRIANGLE_THIRD_END)
    {
			DEBUG_USARTx=USART1;
//        printf("t5.txt=\"���ǲ�\"\xff\xff\xff");
//						       printf("���ǲ�/r/n");
				vpp_m+=4;
    }

			
//		if(vpp_m<=300)
//		vpp_m= (uint32_t)pow(vpp_m/0.798, 1.0033);
//	else if(vpp_m<550)
//		vpp_m=(uint32_t)pow(vpp_m/0.791, 1.0015);
//	else if(vpp_m<=617)
//		vpp_m=(uint32_t)pow(vpp_m/0.788,1.0032);
//	else if (vpp_m)
//		vpp_m=(uint32_t)pow(vpp_m/0.788,1.005);
//	if(vpp_m<210&&vpp_m>58)
//		vpp_m-=4;
//	else if(vpp_m>=210&&vpp_m<500)
//		vpp_m-=2;
//	else if(vpp_m>850)
//		vpp_m+=2;
//	if(vpp_m>940)
//		vpp_m+=2;
	
//	if(Freq>9980&&Freq<=20100)
//		vpp_m-=3;
//	else if(Freq>20100&&Freq<=30100)
//		vpp_m-=6;
//	else if(Freq>30100&&Freq<=40100)
//		vpp_m-=6;
//	else if(Freq>40100&&Freq<=50100)
//		vpp_m-=9;
//	else if(Freq>50100&&Freq<=60100)
//		vpp_m-=9;
//	else if(Freq>60100&&Freq<=70100)
//		vpp_m-=12;
//	else if(Freq>70100&&Freq<=80100)
//		vpp_m-=12;
//	else if(Freq>80100&&Freq<=90100)
//		vpp_m-=15;
//	else if(Freq>90100&&Freq<=100100)
//		vpp_m-=18;
	
//	if(Freq>9980&&Freq<=20100)
//		vpp_m=vpp_m*0.96;
//	else if(Freq>20100&&Freq<=30100)
//		vpp_m=vpp_m*0.96;
//	else if(Freq>30100&&Freq<=40100)
//		vpp_m=vpp_m;
//	else if(Freq>40100&&Freq<=50100)
//		vpp_m=vpp_m;
//	else if(Freq>50100&&Freq<=60100)
//		vpp_m=vpp_m*0.94;
//	else if(Freq>60100&&Freq<=70100)
//		vpp_m=vpp_m*0.94;
//	else if(Freq>70100&&Freq<=80100)
//		vpp_m=vpp_m*0.94;
//	else if(Freq>80100&&Freq<=90100)
//		vpp_m=vpp_m*0.87;
//	else if(Freq>90100&&Freq<=100100)
//		vpp_m=vpp_m*0.94;
	
		DEBUG_USARTx=USART1;
	
	
	
//	printf("x1.val=%d\xff\xff\xff",vpp_m*100);

//		printf("{b}%.2fmv\r\n",calculate_peak_avg(ADC_ConvertedValue, 100)*1000*0.9*3.3f/4096);
		
//		printf("{b}%.2fmv\r\n",vpp*1000*0.9*3.3f/4096);
		
//		printf("x1.val=%d\xff\xff\xff",(uint32_t)vpp*3.3*1000/4095/3.0);
		
		TIM_Cmd(TIM3,ENABLE); 
		
		DMA_ClearITPendingBit(DMA2_Stream0, DMA_IT_TCIF0);
	}
}

void Rheostat_Init(void)
{
	Rheostat_ADC_GPIO_Config(); 
	Rheostat_ADC_Mode_Config();
	arm_cfft_radix4_init_f32(&scfft,FFT_LENGTH,0,1);
	Tim3_Init(arrv-1,pscv-1);
}
